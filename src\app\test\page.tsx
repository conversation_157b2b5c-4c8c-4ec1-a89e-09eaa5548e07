'use client';

import { useState } from 'react';
import Link from 'next/link';
import { generateRandomInvoiceLocal } from '@/utils/invoiceGenerator';
import { InvoiceType } from '@/types/invoice';

export default function TestPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testLocalGeneration = async () => {
    setLoading(true);
    setResult('');
    
    try {
      const invoice = await generateRandomInvoiceLocal('<EMAIL>', InvoiceType.WINDSURF, 'zh_CN');
      setResult(JSON.stringify(invoice, null, 2));
    } catch (error) {
      setResult(`错误: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">本地生成器测试</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <button
            onClick={testLocalGeneration}
            disabled={loading}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50"
          >
            {loading ? '生成中...' : '🏠 测试本地生成'}
          </button>
          
          <div className="mt-4">
            <Link href="/" className="text-blue-600 hover:underline">← 返回主页</Link>
          </div>
        </div>
        
        {result && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">生成结果:</h2>
            <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
              {result}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
