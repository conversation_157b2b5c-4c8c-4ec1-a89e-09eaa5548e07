'use client';

import { useState, useEffect } from 'react';
import WindsurfInvoice from '@/components/WindsurfInvoice';
import CursorInvoice from '@/components/CursorInvoice';
import { generateRandomInvoice, generateRandomInvoiceLocal } from '@/utils/invoiceGenerator';
import { InvoiceData, InvoiceType } from '@/types/invoice';

export default function Home() {
  const [invoiceData, setInvoiceData] = useState<InvoiceData | null>(null);
  const [email, setEmail] = useState<string>('');
  const [emailError, setEmailError] = useState<string>('');
  const [invoiceType, setInvoiceType] = useState<InvoiceType>(InvoiceType.WINDSURF);
  const [selectedCountry, setSelectedCountry] = useState<string>('en_US');

  // 管理页面标题
  useEffect(() => {
    if (!invoiceData) {
      document.title = 'WIPDF - 随机Invoice生成器';
    }
  }, [invoiceData]);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleGenerateInvoice = async () => {
    // 清除之前的错误
    setEmailError('');

    // 验证邮箱
    if (!email.trim()) {
      setEmailError('请输入邮箱地址');
      return;
    }

    if (!validateEmail(email.trim())) {
      setEmailError('请输入有效的邮箱地址');
      return;
    }

    const newInvoice = await generateRandomInvoice(email.trim(), invoiceType, selectedCountry);
    setInvoiceData(newInvoice);

    // 动态修改页面标题，用于PDF文件名
    const invoiceTypeName = newInvoice.type === InvoiceType.CURSOR ? 'Cursor' : 'Windsurf';
    document.title = `Invoice_${invoiceTypeName}_${newInvoice.invoiceNumber}`;
  };

  const handleGenerateInvoiceLocal = async () => {
    // 清除之前的错误
    setEmailError('');

    // 验证邮箱
    if (!email.trim()) {
      setEmailError('请输入邮箱地址');
      return;
    }

    if (!validateEmail(email.trim())) {
      setEmailError('请输入有效的邮箱地址');
      return;
    }

    const newInvoice = await generateRandomInvoiceLocal(email.trim(), invoiceType, selectedCountry);
    setInvoiceData(newInvoice);

    // 动态修改页面标题，用于PDF文件名
    const invoiceTypeName = newInvoice.type === InvoiceType.CURSOR ? 'Cursor' : 'Windsurf';
    document.title = `Invoice_${invoiceTypeName}_${newInvoice.invoiceNumber}_Local`;
  };

  const handlePrint = () => {
    // 清空页面标题以避免在PDF中显示详细信息
    const originalTitle = document.title;
    document.title = ''; // 或者使用通用标题如 'Invoice'

    window.print();

    // 打印后恢复原标题
    setTimeout(() => {
      document.title = originalTitle;
    }, 1000);
  };



  return (
    <div className="min-h-screen bg-gray-100">
      {/* 控制面板 */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-4 print:hidden">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">随机Invoice生成器</h1>
              <p className="text-gray-600">支持生成 Windsurf 和 Cursor Invoice模板的随机Invoice数据</p>
              <div className="mt-2">
                <a
                  href="/test"
                  className="text-sm text-blue-600 hover:text-blue-800 underline"
                >
                  🧪 测试本地生成器 →
                </a>
              </div>
            </div>
            {invoiceData && (
              <button
                onClick={handlePrint}
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                打印/保存PDF
              </button>
            )}
          </div>

          {/* Invoice类型选择器 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Invoice类型
            </label>
            <div className="flex gap-6">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="invoiceType"
                  value={InvoiceType.WINDSURF}
                  checked={invoiceType === InvoiceType.WINDSURF}
                  onChange={(e) => setInvoiceType(e.target.value as InvoiceType)}
                  className="mr-2 text-blue-600"
                />
                <span className="text-sm text-gray-700">Windsurf Invoice ($6.90)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="invoiceType"
                  value={InvoiceType.WINDSURF_15}
                  checked={invoiceType === InvoiceType.WINDSURF_15}
                  onChange={(e) => setInvoiceType(e.target.value as InvoiceType)}
                  className="mr-2 text-blue-600"
                />
                <span className="text-sm text-gray-700">Windsurf Invoice ($15.00)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="invoiceType"
                  value={InvoiceType.CURSOR}
                  checked={invoiceType === InvoiceType.CURSOR}
                  onChange={(e) => setInvoiceType(e.target.value as InvoiceType)}
                  className="mr-2 text-blue-600"
                />
                <span className="text-sm text-gray-700">Cursor Invoice ($20.00)</span>
              </label>
            </div>
          </div>

          {/* 国家选择 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              收票人国家/地区
            </label>
            <select
              value={selectedCountry}
              onChange={(e) => setSelectedCountry(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {/* 亚洲 */}
              <optgroup label="🌏 亚洲 (Asia)">
                <option value="zh_CN">中国 (China)</option>
                <option value="zh_TW">台湾 (Taiwan)</option>
                <option value="ja_JP">日本 (Japan)</option>
                <option value="ko_KR">韩国 (South Korea)</option>
                <option value="en_HK">香港 (Hong Kong)</option>
                <option value="en_SG">新加坡 (Singapore)</option>
                <option value="ms_MY">马来西亚 (Malaysia)</option>
                <option value="th_TH">泰国 (Thailand)</option>
                <option value="vi_VN">越南 (Vietnam)</option>
                <option value="en_PH">菲律宾 (Philippines)</option>
                <option value="id_ID">印度尼西亚 (Indonesia)</option>
                <option value="en_IN">印度 (India)</option>
                <option value="bn_BD">孟加拉国 (Bangladesh)</option>
                <option value="ne_NP">尼泊尔 (Nepal)</option>
                <option value="fa_IR">伊朗 (Iran)</option>
                <option value="he_IL">以色列 (Israel)</option>
                <option value="tr_TR">土耳其 (Turkey)</option>
                <option value="kk_KZ">哈萨克斯坦 (Kazakhstan)</option>
                <option value="mn_MN">蒙古 (Mongolia)</option>
                <option value="hy_AM">亚美尼亚 (Armenia)</option>
                <option value="ka_GE">格鲁吉亚 (Georgia)</option>
              </optgroup>

              {/* 欧洲 */}
              <optgroup label="🌍 欧洲 (Europe)">
                <option value="en_GB">英国 (United Kingdom)</option>
                <option value="de_DE">德国 (Germany)</option>
                <option value="fr_FR">法国 (France)</option>
                <option value="it_IT">意大利 (Italy)</option>
                <option value="es_ES">西班牙 (Spain)</option>
                <option value="ru_RU">俄罗斯 (Russia)</option>
                <option value="pl_PL">波兰 (Poland)</option>
                <option value="nl_NL">荷兰 (Netherlands)</option>
                <option value="sv_SE">瑞典 (Sweden)</option>
                <option value="nb_NO">挪威 (Norway)</option>
                <option value="da_DK">丹麦 (Denmark)</option>
                <option value="fi_FI">芬兰 (Finland)</option>
                <option value="is_IS">冰岛 (Iceland)</option>
                <option value="at_AT">奥地利 (Austria)</option>
                <option value="de_AT">奥地利德语 (Austria German)</option>
                <option value="de_CH">瑞士德语 (Swiss German)</option>
                <option value="fr_CH">瑞士法语 (Swiss French)</option>
                <option value="it_CH">瑞士意大利语 (Swiss Italian)</option>
                <option value="fr_BE">比利时法语 (Belgium French)</option>
                <option value="nl_BE">比利时荷兰语 (Belgium Dutch)</option>
                <option value="pt_PT">葡萄牙 (Portugal)</option>
                <option value="el_GR">希腊 (Greece)</option>
                <option value="el_CY">塞浦路斯希腊语 (Cyprus Greek)</option>
                <option value="cs_CZ">捷克 (Czech Republic)</option>
                <option value="sk_SK">斯洛伐克 (Slovakia)</option>
                <option value="hu_HU">匈牙利 (Hungary)</option>
                <option value="ro_RO">罗马尼亚 (Romania)</option>
                <option value="ro_MD">摩尔多瓦 (Moldova)</option>
                <option value="bg_BG">保加利亚 (Bulgaria)</option>
                <option value="hr_HR">克罗地亚 (Croatia)</option>
                <option value="sl_SI">斯洛文尼亚 (Slovenia)</option>
                <option value="sr_RS">塞尔维亚 (Serbia)</option>
                <option value="sr_Latn_RS">塞尔维亚拉丁文 (Serbia Latin)</option>
                <option value="sr_Cyrl_RS">塞尔维亚西里尔文 (Serbia Cyrillic)</option>
                <option value="me_ME">黑山 (Montenegro)</option>
                <option value="lt_LT">立陶宛 (Lithuania)</option>
                <option value="lv_LV">拉脱维亚 (Latvia)</option>
                <option value="et_EE">爱沙尼亚 (Estonia)</option>
                <option value="uk_UA">乌克兰 (Ukraine)</option>
              </optgroup>

              {/* 北美洲 */}
              <optgroup label="🌎 北美洲 (North America)">
                <option value="en_US">美国 (United States)</option>
                <option value="en_CA">加拿大 (Canada)</option>
                <option value="fr_CA">加拿大法语 (Canada French)</option>
              </optgroup>

              {/* 南美洲 */}
              <optgroup label="🌎 南美洲 (South America)">
                <option value="pt_BR">巴西 (Brazil)</option>
                <option value="es_AR">阿根廷 (Argentina)</option>
                <option value="es_PE">秘鲁 (Peru)</option>
                <option value="es_VE">委内瑞拉 (Venezuela)</option>
              </optgroup>

              {/* 非洲 */}
              <optgroup label="🌍 非洲 (Africa)">
                <option value="ar_EG">埃及 (Egypt)</option>
                <option value="en_ZA">南非 (South Africa)</option>
                <option value="en_NG">尼日利亚 (Nigeria)</option>
                <option value="en_UG">乌干达 (Uganda)</option>
              </optgroup>

              {/* 大洋洲 */}
              <optgroup label="🌏 大洋洲 (Oceania)">
                <option value="en_AU">澳大利亚 (Australia)</option>
                <option value="en_NZ">新西兰 (New Zealand)</option>
              </optgroup>

              {/* 中东 */}
              <optgroup label="🕌 中东 (Middle East)">
                <option value="ar_SA">沙特阿拉伯 (Saudi Arabia)</option>
                <option value="ar_JO">约旦 (Jordan)</option>
              </optgroup>
            </select>
          </div>

          {/* 邮箱输入区域 */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-end">
            <div className="flex-1 max-w-md">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                收票人邮箱地址 <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="请输入邮箱地址"
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  emailError ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {emailError && (
                <p className="mt-1 text-sm text-red-600">{emailError}</p>
              )}
            </div>
            <div className="flex gap-2">
              <button
                onClick={handleGenerateInvoice}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors whitespace-nowrap"
              >
                生成新Invoice
              </button>
              <button
                onClick={handleGenerateInvoiceLocal}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors whitespace-nowrap flex items-center gap-2"
                title="使用本地数据生成器，无需网络连接"
              >
                <span className="text-sm">🏠</span>
                本地生成
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Invoice显示区域 */}
      <div className="py-2">
        {invoiceData ? (
          invoiceData.type === InvoiceType.CURSOR ? (
            <CursorInvoice data={invoiceData} />
          ) : (
            <WindsurfInvoice data={invoiceData} />
          )
        ) : (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-center max-w-md">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无Invoice</h3>
              <p className="mt-1 text-sm text-gray-500">
                请在上方输入邮箱地址，然后点击&quot;生成新Invoice&quot;按钮开始创建随机Invoice
              </p>
            </div>
          </div>
        )}
      </div>

      {/* 功能说明 */}
      <div className="bg-white border-t border-gray-200 p-6 print:hidden">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">功能说明</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">随机数据生成</h3>
              <p className="text-sm text-gray-600">
                自动生成Invoice号码、收据号码、支付方式、收票人信息和账单日期
              </p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">🏠 本地生成器</h3>
              <p className="text-sm text-gray-600">
                支持23种语言/地区的本地化姓名生成，无需网络连接，毫秒级响应
              </p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">PDF导出</h3>
              <p className="text-sm text-gray-600">
                点击&quot;打印/保存PDF&quot;按钮可以将Invoice保存为PDF文件
              </p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">多种模板</h3>
              <p className="text-sm text-gray-600">
                支持 Windsurf 和 Cursor 两种Invoice模板，完全保持原始样式和布局
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
