// Faker API 备选方案 - 本地数据生成器
import { BillToInfo } from '@/types/invoice';

// 完整的姓名数据库 - 支持所有地区
const NAME_DATABASE: Record<string, { firstNames: string[], lastNames: string[] }> = {
  // === 亚洲 ===
  // 中文姓名
  'zh_CN': {
    firstNames: ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀英', '霞', '平'],
    lastNames: ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
  },
  'zh_TW': {
    firstNames: ['志明', '春嬌', '美麗', '建國', '淑芬', '雅婷', '俊傑', '怡君', '宗翰', '佩君', '家豪', '雅雯', '承翰', '怡萱', '宗憲', '佳穎'],
    lastNames: ['陳', '林', '黃', '張', '李', '王', '吳', '劉', '蔡', '楊', '許', '鄭', '謝', '洪', '郭', '邱', '曾', '廖', '賴', '徐']
  },
  
  // 日文姓名
  'ja_JP': {
    firstNames: ['太郎', '花子', '一郎', '美咲', '翔太', '結衣', '大輔', '愛', '健太', '美穂', '拓也', '麻衣', '雄太', '恵', '慎一', '由美'],
    lastNames: ['田中', '佐藤', '鈴木', '高橋', '渡辺', '伊藤', '山本', '中村', '小林', '加藤', '吉田', '山田', '佐々木', '山口', '松本', '井上']
  },
  
  // 韩文姓名
  'ko_KR': {
    firstNames: ['민수', '지영', '현우', '수진', '준호', '미영', '성민', '혜진', '동현', '은지', '재현', '소영', '우진', '예은', '태현', '다은'],
    lastNames: ['김', '이', '박', '최', '정', '강', '조', '윤', '장', '임', '한', '오', '서', '신', '권', '황', '안', '송', '류', '전']
  },
  
  // 东南亚英文姓名
  'en_HK': {
    firstNames: ['Wing', 'Mei', 'Ka', 'Wai', 'Ming', 'Ling', 'Chun', 'Yuen', 'Ho', 'Fai', 'Siu', 'Kit', 'Man', 'Yan', 'Lok', 'Sum'],
    lastNames: ['Chan', 'Wong', 'Lee', 'Lau', 'Ng', 'Cheung', 'Tang', 'Leung', 'Lam', 'Tsang', 'Ho', 'Yu', 'Lai', 'Fong', 'Mak', 'Tse']
  },
  'en_SG': {
    firstNames: ['Wei Ming', 'Li Hua', 'Raj', 'Priya', 'Ahmad', 'Siti', 'David', 'Sarah', 'Kumar', 'Meera', 'Hassan', 'Fatima', 'James', 'Michelle', 'Arjun', 'Kavitha'],
    lastNames: ['Tan', 'Lim', 'Lee', 'Ng', 'Ong', 'Teo', 'Goh', 'Chua', 'Koh', 'Sim', 'Low', 'Yeo', 'Chong', 'Ho', 'Seah', 'Wee']
  },
  'ms_MY': {
    firstNames: ['Ahmad', 'Siti', 'Muhammad', 'Nur', 'Ali', 'Fatimah', 'Hassan', 'Aminah', 'Ibrahim', 'Khadijah', 'Omar', 'Zainab', 'Ismail', 'Maryam', 'Yusof', 'Aishah'],
    lastNames: ['Abdullah', 'Rahman', 'Ahmad', 'Ali', 'Hassan', 'Ibrahim', 'Ismail', 'Omar', 'Yusof', 'Mohamed', 'Mahmud', 'Hamid', 'Rashid', 'Karim', 'Salleh', 'Mansor']
  },
  'th_TH': {
    firstNames: ['สมชาย', 'สมหญิง', 'วิชัย', 'วันทนา', 'สุรชัย', 'สุมาลี', 'ประยุทธ', 'ประภา', 'วิทยา', 'วิไล', 'สมศักดิ์', 'สมใจ', 'ชาญ', 'ชนิดา', 'ธนา', 'ธิดา'],
    lastNames: ['จันทร์', 'แสง', 'ทอง', 'เงิน', 'แก้ว', 'ใส', 'สว่าง', 'ดี', 'งาม', 'สุข', 'รุ่ง', 'เจริญ', 'มั่น', 'คง', 'ยั่ง', 'ยืน']
  },
  'vi_VN': {
    firstNames: ['Minh', 'Linh', 'Hùng', 'Hương', 'Tuấn', 'Thảo', 'Dũng', 'Dung', 'Hải', 'Hạnh', 'Long', 'Lan', 'Nam', 'Nga', 'Phong', 'Phương'],
    lastNames: ['Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Huỳnh', 'Phan', 'Vũ', 'Võ', 'Đặng', 'Bùi', 'Đỗ', 'Hồ', 'Ngô', 'Dương', 'Lý']
  },
  
  'en_PH': {
    firstNames: ['Jose', 'Maria', 'Juan', 'Ana', 'Antonio', 'Rosa', 'Francisco', 'Carmen', 'Manuel', 'Josefa', 'Pedro', 'Luz', 'Jesus', 'Esperanza', 'Luis', 'Remedios'],
    lastNames: ['Santos', 'Reyes', 'Cruz', 'Bautista', 'Ocampo', 'Garcia', 'Mendoza', 'Torres', 'Tomas', 'Andres', 'Marquez', 'Castillo', 'Iglesias', 'Villanueva', 'Delos Santos', 'Fernandez']
  },
  'id_ID': {
    firstNames: ['Budi', 'Sari', 'Ahmad', 'Siti', 'Andi', 'Dewi', 'Agus', 'Rina', 'Hendra', 'Maya', 'Rudi', 'Indira', 'Dedi', 'Lestari', 'Bambang', 'Wati'],
    lastNames: ['Santoso', 'Wijaya', 'Kurniawan', 'Sari', 'Pratama', 'Utomo', 'Wibowo', 'Susanto', 'Lestari', 'Handoko', 'Setiawan', 'Rahayu', 'Hidayat', 'Suharto', 'Gunawan', 'Permana']
  },
  'en_IN': {
    firstNames: ['Raj', 'Priya', 'Amit', 'Sunita', 'Suresh', 'Kavita', 'Ravi', 'Meera', 'Anil', 'Geeta', 'Vijay', 'Sita', 'Ramesh', 'Lata', 'Ashok', 'Usha'],
    lastNames: ['Sharma', 'Gupta', 'Singh', 'Kumar', 'Verma', 'Agarwal', 'Jain', 'Bansal', 'Sinha', 'Mishra', 'Pandey', 'Tiwari', 'Yadav', 'Saxena', 'Arora', 'Malhotra']
  },
  'bn_BD': {
    firstNames: ['রহিম', 'ফাতেমা', 'করিম', 'খাদিজা', 'আলী', 'আয়েশা', 'হাসান', 'জয়নব', 'হোসেন', 'রোকেয়া', 'আহমেদ', 'সালমা', 'ইব্রাহিম', 'নাসরিন', 'ওমর', 'রাবেয়া'],
    lastNames: ['আহমেদ', 'আলী', 'খান', 'রহমান', 'ইসলাম', 'হাসান', 'হোসেন', 'শেখ', 'চৌধুরী', 'মিয়া', 'খাতুন', 'বেগম', 'উদ্দিন', 'আক্তার', 'করিম', 'মোল্লা']
  },
  'ne_NP': {
    firstNames: ['राम', 'सीता', 'श्याम', 'गीता', 'हरि', 'लक्ष्मी', 'कृष्ण', 'राधा', 'गोपाल', 'सरस्वती', 'विष्णु', 'पार्वती', 'शिव', 'दुर्गा', 'ब्रह्म', 'काली'],
    lastNames: ['शर्मा', 'अधिकारी', 'गुरुङ', 'तामाङ', 'राई', 'लिम्बू', 'मगर', 'थापा', 'श्रेष्ठ', 'जोशी', 'पौडेल', 'खत्री', 'बस्नेत', 'पन्त', 'उपाध्याय', 'भट्टराई']
  },
  'fa_IR': {
    firstNames: ['محمد', 'فاطمه', 'علی', 'زهرا', 'حسن', 'مریم', 'حسین', 'آسیه', 'احمد', 'خدیجه', 'رضا', 'عایشه', 'مصطفی', 'زینب', 'عمر', 'رقیه'],
    lastNames: ['احمدی', 'محمدی', 'حسینی', 'رضایی', 'علوی', 'موسوی', 'کریمی', 'رحمانی', 'نوری', 'صادقی', 'حسنی', 'فاطمی', 'جعفری', 'طاهری', 'باقری', 'نجفی']
  },
  'he_IL': {
    firstNames: ['דוד', 'שרה', 'משה', 'רחל', 'יוסף', 'לאה', 'אברהם', 'רבקה', 'יעקב', 'מרים', 'יצחק', 'אסתר', 'שמואל', 'רות', 'אהרן', 'נעמי'],
    lastNames: ['כהן', 'לוי', 'מזרחי', 'פרידמן', 'דהן', 'אברמוביץ', 'ביטון', 'אוחנה', 'שפירא', 'פרץ', 'אזולאי', 'מלכה', 'חדד', 'בן דוד', 'יוסף', 'אליהו']
  },
  'tr_TR': {
    firstNames: ['Mehmet', 'Ayşe', 'Mustafa', 'Fatma', 'Ahmet', 'Hatice', 'Ali', 'Zeynep', 'Hüseyin', 'Emine', 'Hasan', 'Elif', 'İbrahim', 'Merve', 'Ömer', 'Özlem'],
    lastNames: ['Yılmaz', 'Kaya', 'Demir', 'Şahin', 'Çelik', 'Yıldız', 'Yıldırım', 'Öztürk', 'Aydin', 'Özdemir', 'Arslan', 'Doğan', 'Kılıç', 'Aslan', 'Çetin', 'Kara']
  },
  'kk_KZ': {
    firstNames: ['Нұрсұлтан', 'Айгүл', 'Ерлан', 'Гүлнар', 'Серік', 'Алма', 'Мұрат', 'Жанар', 'Асхат', 'Дина', 'Болат', 'Сауле', 'Ерболат', 'Айжан', 'Қайрат', 'Гүлмира'],
    lastNames: ['Назарбаев', 'Тоқаев', 'Мәсімов', 'Сағынтаев', 'Мәмин', 'Исекешев', 'Қасымов', 'Сарсенбаев', 'Жақсылықов', 'Тасмағамбетов', 'Ахметов', 'Құлибаев', 'Субханбердин', 'Есімов', 'Қожахметов', 'Байменов']
  },
  'mn_MN': {
    firstNames: ['Батбаяр', 'Оюунчимэг', 'Болд', 'Сайханцэцэг', 'Энхбаяр', 'Цэцэгмаа', 'Ганбаатар', 'Номинцэцэг', 'Мөнхбаяр', 'Алтанцэцэг', 'Пүрэвбаяр', 'Энхцэцэг', 'Батмөнх', 'Цагаанцэцэг', 'Энхболд', 'Мөнхцэцэг'],
    lastNames: ['Батбаяр', 'Энхбаяр', 'Болдбаатар', 'Ганбаатар', 'Мөнхбаяр', 'Пүрэвбаяр', 'Батмөнх', 'Энхболд', 'Цэндбаяр', 'Алтанбаяр', 'Сайханбаяр', 'Төмөрбаяр', 'Жавхланбаяр', 'Цэрэнбаяр', 'Дашбаяр', 'Нямбаяр']
  },
  'hy_AM': {
    firstNames: ['Արամ', 'Անահիտ', 'Դավիթ', 'Մարիամ', 'Հայկ', 'Արփինե', 'Վահան', 'Սիրանուշ', 'Արմեն', 'Նվարդ', 'Գարեգին', 'Զարուհի', 'Ռուբեն', 'Լուսինե', 'Արշակ', 'Գայանե'],
    lastNames: ['Հակոբյան', 'Պետրոսյան', 'Գրիգորյան', 'Ավետիսյան', 'Մարտիրոսյան', 'Ստեփանյան', 'Ղազարյան', 'Վարդանյան', 'Մանուկյան', 'Սարգսյան', 'Ալեքսանյան', 'Դավթյան', 'Մկրտչյան', 'Հովհաննիսյան', 'Բաղդասարյան', 'Կարապետյան']
  },
  'ka_GE': {
    firstNames: ['გიორგი', 'ნინო', 'დავით', 'მარიამ', 'ალექსანდრე', 'ანა', 'ირაკლი', 'ნათია', 'ლევან', 'თამარ', 'ზურაბ', 'ეკა', 'ნიკა', 'მაია', 'ვახტანგ', 'ნანა'],
    lastNames: ['ბერიძე', 'კვარაცხელია', 'ლობჟანიძე', 'ღლონტი', 'ჯაფარიძე', 'ღუდუშაური', 'ცხოვრებაძე', 'ღვინიაშვილი', 'ღარიბაშვილი', 'მამედოვი', 'ხაჩიძე', 'შენგელია', 'კაპანაძე', 'ჩხეიძე', 'მეგრელიშვილი', 'ღუღუნიშვილი']
  },

  // === 欧洲 ===
  // 英文姓名 (英国系)
  'en_GB': {
    firstNames: ['Oliver', 'Amelia', 'George', 'Isla', 'Harry', 'Ava', 'Noah', 'Mia', 'Jack', 'Isabella', 'Jacob', 'Sophia', 'Leo', 'Grace', 'Oscar', 'Lily'],
    lastNames: ['Smith', 'Jones', 'Taylor', 'Williams', 'Brown', 'Davies', 'Evans', 'Wilson', 'Thomas', 'Roberts', 'Johnson', 'Lewis', 'Walker', 'Robinson', 'Wood', 'Thompson']
  },  
  // 北美英文姓名
  'en_US': {
    firstNames: ['James', 'Mary', 'John', 'Patricia', 'Robert', 'Jennifer', 'Michael', 'Linda', 'William', 'Elizabeth', 'David', 'Barbara', 'Richard', 'Susan', 'Joseph', 'Jessica'],
    lastNames: ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas']
  },
  'en_CA': {
    firstNames: ['Liam', 'Emma', 'Noah', 'Olivia', 'William', 'Ava', 'James', 'Isabella', 'Benjamin', 'Sophia', 'Lucas', 'Charlotte', 'Henry', 'Mia', 'Alexander', 'Amelia'],
    lastNames: ['Smith', 'Brown', 'Tremblay', 'Martin', 'Roy', 'Wilson', 'MacDonald', 'Johnson', 'Taylor', 'Anderson', 'Campbell', 'Lee', 'White', 'Thompson', 'Moore', 'Young']
  },
  'fr_CA': {
    firstNames: ['Félix', 'Emma', 'William', 'Olivia', 'Liam', 'Charlotte', 'Noah', 'Alice', 'Thomas', 'Béatrice', 'Jacob', 'Rosalie', 'Raphaël', 'Juliette', 'Antoine', 'Zoé'],
    lastNames: ['Tremblay', 'Gagnon', 'Roy', 'Côté', 'Bouchard', 'Gauthier', 'Morin', 'Lavoie', 'Fortin', 'Gagné', 'Ouellet', 'Pelletier', 'Bélanger', 'Lévesque', 'Bergeron', 'Leblanc']
  },
  
  // 德文姓名
  'de_DE': {
    firstNames: ['Ben', 'Emma', 'Paul', 'Mia', 'Leon', 'Hannah', 'Finn', 'Sofia', 'Noah', 'Emilia', 'Louis', 'Lina', 'Henry', 'Marie', 'Felix', 'Lea'],
    lastNames: ['Müller', 'Schmidt', 'Schneider', 'Fischer', 'Weber', 'Meyer', 'Wagner', 'Becker', 'Schulz', 'Hoffmann', 'Schäfer', 'Koch', 'Bauer', 'Richter', 'Klein', 'Wolf']
  },
  'de_AT': {
    firstNames: ['Maximilian', 'Anna', 'Alexander', 'Laura', 'Paul', 'Lena', 'Elias', 'Emma', 'Jakob', 'Leonie', 'David', 'Sophie', 'Noah', 'Marie', 'Simon', 'Johanna'],
    lastNames: ['Gruber', 'Huber', 'Bauer', 'Wagner', 'Müller', 'Pichler', 'Steiner', 'Moser', 'Mayer', 'Hofer', 'Leitner', 'Berger', 'Fuchs', 'Eder', 'Fischer', 'Schmid']
  },
  'de_CH': {
    firstNames: ['Noah', 'Mia', 'Liam', 'Emma', 'Matteo', 'Sofia', 'Leon', 'Lina', 'Gabriel', 'Ella', 'Luis', 'Lia', 'David', 'Alina', 'Elias', 'Laura'],
    lastNames: ['Müller', 'Meier', 'Schmid', 'Keller', 'Weber', 'Huber', 'Schneider', 'Meyer', 'Steiner', 'Fischer', 'Gerber', 'Brunner', 'Baumann', 'Frei', 'Zimmermann', 'Moser']
  },
  
  // 法文姓名
  'fr_FR': {
    firstNames: ['Louis', 'Emma', 'Gabriel', 'Jade', 'Raphaël', 'Louise', 'Arthur', 'Alice', 'Lucas', 'Chloé', 'Adam', 'Lina', 'Hugo', 'Rose', 'Maël', 'Anna'],
    lastNames: ['Martin', 'Bernard', 'Thomas', 'Petit', 'Robert', 'Richard', 'Durand', 'Dubois', 'Moreau', 'Laurent', 'Simon', 'Michel', 'Lefebvre', 'Leroy', 'Roux', 'David']
  },
  'fr_CH': {
    firstNames: ['Gabriel', 'Emma', 'Liam', 'Chloé', 'Noah', 'Léa', 'Arthur', 'Zoé', 'Louis', 'Alice', 'Jules', 'Camille', 'Adam', 'Inès', 'Lucas', 'Manon'],
    lastNames: ['Müller', 'Schmid', 'Schneider', 'Meyer', 'Weber', 'Huber', 'Martin', 'Bernard', 'Dubois', 'Thomas', 'Robert', 'Richard', 'Petit', 'Durand', 'Leroy', 'Moreau']
  },
  'fr_BE': {
    firstNames: ['Arthur', 'Emma', 'Louis', 'Olivia', 'Noah', 'Louise', 'Gabriel', 'Alice', 'Jules', 'Chloé', 'Adam', 'Camille', 'Lucas', 'Léa', 'Hugo', 'Zoé'],
    lastNames: ['Peeters', 'Janssens', 'Maes', 'Jacobs', 'Mertens', 'Willems', 'Claes', 'Goossens', 'Wouters', 'De Smet', 'De Meyer', 'Pauwels', 'De Cock', 'Smet', 'Vermeulen', 'Van Den Berg']
  },  
  // 意大利文姓名
  'it_IT': {
    firstNames: ['Francesco', 'Sofia', 'Alessandro', 'Giulia', 'Lorenzo', 'Aurora', 'Leonardo', 'Alice', 'Andrea', 'Ginevra', 'Gabriele', 'Emma', 'Matteo', 'Giorgia', 'Riccardo', 'Beatrice'],
    lastNames: ['Rossi', 'Russo', 'Ferrari', 'Esposito', 'Bianchi', 'Romano', 'Colombo', 'Ricci', 'Marino', 'Greco', 'Bruno', 'Gallo', 'Conti', 'De Luca', 'Mancini', 'Costa']
  },
  'it_CH': {
    firstNames: ['Leonardo', 'Sofia', 'Francesco', 'Giulia', 'Alessandro', 'Aurora', 'Lorenzo', 'Alice', 'Matteo', 'Emma', 'Andrea', 'Ginevra', 'Gabriele', 'Beatrice', 'Riccardo', 'Giorgia'],
    lastNames: ['Bernasconi', 'Cattaneo', 'Villa', 'Ferrari', 'Colombo', 'Bianchi', 'Rossi', 'Russo', 'Martini', 'Galli', 'Conti', 'Romano', 'Ricci', 'Greco', 'Marino', 'Bruno']
  },
  
  // 西班牙文姓名
  'es_ES': {
    firstNames: ['Hugo', 'Lucía', 'Martín', 'María', 'Daniel', 'Paula', 'Pablo', 'Emma', 'Alejandro', 'Daniela', 'Adrián', 'Carla', 'Álvaro', 'Sara', 'Manuel', 'Sofía'],
    lastNames: ['García', 'Rodríguez', 'González', 'Fernández', 'López', 'Martínez', 'Sánchez', 'Pérez', 'Gómez', 'Martín', 'Jiménez', 'Ruiz', 'Hernández', 'Díaz', 'Moreno', 'Muñoz']
  },
  
  // 荷兰文姓名
  'nl_NL': {
    firstNames: ['Daan', 'Emma', 'Lucas', 'Tess', 'Milan', 'Sophie', 'Sem', 'Fenna', 'Levi', 'Zoë', 'Finn', 'Liv', 'Noud', 'Nora', 'Mason', 'Mila'],
    lastNames: ['De Jong', 'Jansen', 'De Vries', 'Van Den Berg', 'Van Dijk', 'Bakker', 'Janssen', 'Visser', 'Smit', 'Meijer', 'De Boer', 'Mulder', 'De Groot', 'Bos', 'Vos', 'Peters']
  },
  'nl_BE': {
    firstNames: ['Arthur', 'Emma', 'Noah', 'Olivia', 'Louis', 'Louise', 'Jules', 'Alice', 'Gabriel', 'Chloé', 'Adam', 'Camille', 'Lucas', 'Léa', 'Hugo', 'Zoé'],
    lastNames: ['Peeters', 'Janssens', 'Maes', 'Jacobs', 'Mertens', 'Willems', 'Claes', 'Goossens', 'Wouters', 'De Smet', 'De Meyer', 'Pauwels', 'De Cock', 'Smet', 'Vermeulen', 'Van Den Berg']
  },
  
  // 葡萄牙文姓名
  'pt_PT': {
    firstNames: ['Francisco', 'Leonor', 'João', 'Matilde', 'Afonso', 'Beatriz', 'Duarte', 'Carolina', 'Gonçalo', 'Mariana', 'Tomás', 'Inês', 'Miguel', 'Ana', 'Pedro', 'Margarida'],
    lastNames: ['Silva', 'Santos', 'Ferreira', 'Pereira', 'Oliveira', 'Costa', 'Rodrigues', 'Martins', 'Jesus', 'Sousa', 'Fernandes', 'Gonçalves', 'Gomes', 'Lopes', 'Marques', 'Alves']
  },
  'pt_BR': {
    firstNames: ['Miguel', 'Alice', 'Arthur', 'Sophia', 'Bernardo', 'Helena', 'Heitor', 'Valentina', 'Davi', 'Laura', 'Lorenzo', 'Isabella', 'Théo', 'Manuela', 'Pedro', 'Júlia'],
    lastNames: ['Silva', 'Santos', 'Oliveira', 'Souza', 'Rodrigues', 'Ferreira', 'Alves', 'Pereira', 'Lima', 'Gomes', 'Ribeiro', 'Carvalho', 'Almeida', 'Lopes', 'Soares', 'Fernandes']
  },  
  // 俄文姓名
  'ru_RU': {
    firstNames: ['Александр', 'Анна', 'Михаил', 'Елена', 'Максим', 'Ольга', 'Артём', 'Татьяна', 'Дмитрий', 'Наталья', 'Никита', 'Ирина', 'Иван', 'Светлана', 'Алексей', 'Мария'],
    lastNames: ['Иванов', 'Смирнов', 'Кузнецов', 'Попов', 'Васильев', 'Петров', 'Соколов', 'Михайлов', 'Новиков', 'Фёдоров', 'Морозов', 'Волков', 'Алексеев', 'Лебедев', 'Семёнов', 'Егоров']
  },
  
  // 北欧姓名
  'sv_SE': {
    firstNames: ['William', 'Alice', 'Liam', 'Maja', 'Noah', 'Elsa', 'Hugo', 'Alma', 'Oliver', 'Astrid', 'Oscar', 'Vera', 'Lucas', 'Ebba', 'Elias', 'Freja'],
    lastNames: ['Andersson', 'Johansson', 'Karlsson', 'Nilsson', 'Eriksson', 'Larsson', 'Olsson', 'Persson', 'Svensson', 'Gustafsson', 'Pettersson', 'Jonsson', 'Jansson', 'Hansson', 'Bengtsson', 'Jönsson']
  },
  'nb_NO': {
    firstNames: ['Jakob', 'Emma', 'Emil', 'Nora', 'Oliver', 'Ella', 'William', 'Maja', 'Lucas', 'Emilie', 'Filip', 'Sofie', 'Liam', 'Leah', 'Henrik', 'Sara'],
    lastNames: ['Hansen', 'Johansen', 'Olsen', 'Larsen', 'Andersen', 'Pedersen', 'Nilsen', 'Kristiansen', 'Jensen', 'Karlsen', 'Johnsen', 'Pettersen', 'Eriksen', 'Berg', 'Haugen', 'Hagen']
  },
  'da_DK': {
    firstNames: ['William', 'Emma', 'Oliver', 'Ida', 'Noah', 'Clara', 'Oscar', 'Laura', 'Lucas', 'Mathilde', 'Carl', 'Sofia', 'Victor', 'Agnes', 'Magnus', 'Alma'],
    lastNames: ['Nielsen', 'Jensen', 'Hansen', 'Pedersen', 'Andersen', 'Christensen', 'Larsen', 'Sørensen', 'Rasmussen', 'Jørgensen', 'Petersen', 'Madsen', 'Kristensen', 'Olsen', 'Thomsen', 'Christiansen']
  },
  'fi_FI': {
    firstNames: ['Väinö', 'Aino', 'Eino', 'Helmi', 'Onni', 'Kerttu', 'Leevi', 'Siiri', 'Elias', 'Aada', 'Oliver', 'Lilja', 'Leo', 'Venla', 'Niilo', 'Pihla'],
    lastNames: ['Korhonen', 'Virtanen', 'Mäkinen', 'Nieminen', 'Mäkelä', 'Hämäläinen', 'Laine', 'Heikkinen', 'Koskinen', 'Järvinen', 'Lehtonen', 'Lehtinen', 'Saarinen', 'Salminen', 'Heinonen', 'Niemi']
  },
  'is_IS': {
    firstNames: ['Aron', 'Guðrún', 'Sigurður', 'Anna', 'Gunnar', 'Kristín', 'Ólafur', 'Margrét', 'Einar', 'Sigríður', 'Þórður', 'Helga', 'Jón', 'Ragnhildur', 'Magnús', 'Ingibjörg'],
    lastNames: ['Jónsson', 'Sigurðsson', 'Guðmundsson', 'Einarsson', 'Magnússon', 'Ólafsson', 'Þórsson', 'Kristjánsson', 'Arnarsson', 'Gunnarsson', 'Stefánsson', 'Ragnarsson', 'Björnsson', 'Þorsteinsson', 'Árnason', 'Friðriksson']
  },
  
  // 东欧姓名
  'pl_PL': {
    firstNames: ['Antoni', 'Zuzanna', 'Jan', 'Julia', 'Aleksander', 'Zofia', 'Franciszek', 'Hanna', 'Jakub', 'Maya', 'Leon', 'Lena', 'Mikołaj', 'Alicja', 'Stanisław', 'Amelia'],
    lastNames: ['Nowak', 'Kowalski', 'Wiśniewski', 'Wójcik', 'Kowalczyk', 'Kamiński', 'Lewandowski', 'Zieliński', 'Szymański', 'Woźniak', 'Dąbrowski', 'Kozłowski', 'Jankowski', 'Mazur', 'Kwiatkowski', 'Krawczyk']
  },
  'cs_CZ': {
    firstNames: ['Jakub', 'Tereza', 'Jan', 'Anna', 'Tomáš', 'Natálie', 'Adam', 'Adéla', 'Matěj', 'Karolína', 'Vojtěch', 'Barbora', 'Lukáš', 'Klára', 'David', 'Eliška'],
    lastNames: ['Novák', 'Svoboda', 'Novotný', 'Dvořák', 'Černý', 'Procházka', 'Kučera', 'Veselý', 'Horák', 'Němec', 'Pokorný', 'Pospíšil', 'Hájek', 'Jelínek', 'Král', 'Růžička']
  },
  'sk_SK': {
    firstNames: ['Jakub', 'Sofia', 'Tomáš', 'Emma', 'Adam', 'Nina', 'Matej', 'Viktória', 'Samuel', 'Natália', 'David', 'Lea', 'Daniel', 'Sára', 'Martin', 'Ema'],
    lastNames: ['Horváth', 'Kováč', 'Varga', 'Tóth', 'Nagy', 'Baláž', 'Szabó', 'Molnár', 'Simon', 'Lukáč', 'Takáč', 'Gál', 'Kočiš', 'Michal', 'Zeman', 'Čech']
  },  'hu_HU': {
    firstNames: ['Bence', 'Anna', 'Máté', 'Emma', 'Levente', 'Hanna', 'Dávid', 'Luca', 'Ádám', 'Lili', 'Zoltán', 'Viktória', 'Péter', 'Réka', 'Gergő', 'Sára'],
    lastNames: ['Nagy', 'Kovács', 'Tóth', 'Szabó', 'Horváth', 'Varga', 'Kiss', 'Molnár', 'Németh', 'Farkas', 'Balogh', 'Papp', 'Takács', 'Juhász', 'Lakatos', 'Mészáros']
  },
  'ro_RO': {
    firstNames: ['Andrei', 'Maria', 'Alexandru', 'Elena', 'Mihai', 'Ana', 'Stefan', 'Ioana', 'Radu', 'Andreea', 'Cristian', 'Alina', 'Adrian', 'Cristina', 'Florin', 'Diana'],
    lastNames: ['Popescu', 'Ionescu', 'Popa', 'Radu', 'Stoica', 'Stan', 'Dumitrescu', 'Dima', 'Constantinescu', 'Marin', 'Nistor', 'Florea', 'Georgescu', 'Tomescu', 'Mocanu', 'Barbu']
  },
  'ro_MD': {
    firstNames: ['Ion', 'Maria', 'Vasile', 'Elena', 'Nicolae', 'Ana', 'Gheorghe', 'Tatiana', 'Sergiu', 'Natalia', 'Andrei', 'Svetlana', 'Victor', 'Irina', 'Pavel', 'Oxana'],
    lastNames: ['Rusu', 'Moraru', 'Botnaru', 'Ciobanu', 'Cojocaru', 'Ungureanu', 'Cazacu', 'Munteanu', 'Lungu', 'Croitoru', 'Rotaru', 'Pascaru', 'Coșciug', 'Țurcanu', 'Gînju', 'Bivol']
  },
  'bg_BG': {
    firstNames: ['Александър', 'Мария', 'Георги', 'Елена', 'Димитър', 'Анна', 'Николай', 'Ивана', 'Иван', 'Петя', 'Стефан', 'Радка', 'Петър', 'Надежда', 'Христо', 'Веселина'],
    lastNames: ['Иванов', 'Георгиев', 'Димитров', 'Петров', 'Николов', 'Христов', 'Стоянов', 'Атанасов', 'Василев', 'Тодоров', 'Ангелов', 'Костов', 'Маринов', 'Станев', 'Русев', 'Колев']
  },
  'hr_HR': {
    firstNames: ['Luka', 'Petra', 'David', 'Ana', 'Mateo', 'Ema', 'Filip', 'Sara', 'Jakov', 'Lucija', 'Noa', 'Mia', 'Leon', 'Nika', 'Roko', 'Lana'],
    lastNames: ['Horvat', 'Novak', 'Marić', 'Petrović', 'Jurić', 'Kovačić', 'Babić', 'Knežević', 'Pavlović', 'Tomić', 'Matić', 'Božić', 'Blažević', 'Grgić', 'Vidović', 'Šimić']
  },
  'sl_SI': {
    firstNames: ['Luka', 'Ema', 'Jakob', 'Sara', 'Mark', 'Zala', 'Žan', 'Lara', 'Tim', 'Nina', 'Gal', 'Eva', 'Nik', 'Maja', 'Jan', 'Pia'],
    lastNames: ['Novak', 'Horvat', 'Krajnc', 'Zupančič', 'Potočnik', 'Kovačič', 'Mlakar', 'Kos', 'Vidmar', 'Golob', 'Kozole', 'Turk', 'Božič', 'Hribar', 'Kastelic', 'Oblak']
  },
  'sr_RS': {
    firstNames: ['Никола', 'Милица', 'Марко', 'Ана', 'Стефан', 'Јована', 'Лука', 'Марија', 'Филип', 'Тамара', 'Александар', 'Андреа', 'Немања', 'Сара', 'Милош', 'Анђела'],
    lastNames: ['Јовановић', 'Петровић', 'Николић', 'Марковић', 'Ђорђевић', 'Стојановић', 'Илић', 'Станковић', 'Павловић', 'Милошевић', 'Живковић', 'Томић', 'Ђукић', 'Радовановић', 'Костић', 'Симић']
  },
  'sr_Latn_RS': {
    firstNames: ['Nikola', 'Milica', 'Marko', 'Ana', 'Stefan', 'Jovana', 'Luka', 'Marija', 'Filip', 'Tamara', 'Aleksandar', 'Andrea', 'Nemanja', 'Sara', 'Miloš', 'Anđela'],
    lastNames: ['Jovanović', 'Petrović', 'Nikolić', 'Marković', 'Đorđević', 'Stojanović', 'Ilić', 'Stanković', 'Pavlović', 'Milošević', 'Živković', 'Tomić', 'Đukić', 'Radovanović', 'Kostić', 'Simić']
  },
  'sr_Cyrl_RS': {
    firstNames: ['Никола', 'Милица', 'Марко', 'Ана', 'Стефан', 'Јована', 'Лука', 'Марија', 'Филип', 'Тамара', 'Александар', 'Андреа', 'Немања', 'Сара', 'Милош', 'Анђела'],
    lastNames: ['Јовановић', 'Петровић', 'Николић', 'Марковић', 'Ђорђевић', 'Стојановић', 'Илић', 'Станковић', 'Павловић', 'Милошевић', 'Живковић', 'Томић', 'Ђукић', 'Радовановић', 'Костић', 'Симић']
  },
  'me_ME': {
    firstNames: ['Nikola', 'Milica', 'Marko', 'Ana', 'Stefan', 'Jovana', 'Luka', 'Marija', 'Filip', 'Tamara', 'Aleksandar', 'Andrea', 'Nemanja', 'Sara', 'Miloš', 'Anđela'],
    lastNames: ['Popović', 'Petrović', 'Nikolić', 'Marković', 'Đorđević', 'Stojanović', 'Ilić', 'Stanković', 'Pavlović', 'Milošević', 'Živković', 'Tomić', 'Đukić', 'Radovanović', 'Kostić', 'Simić']
  },  'lt_LT': {
    firstNames: ['Lukas', 'Emilija', 'Matas', 'Sofija', 'Nojus', 'Liepa', 'Dovydas', 'Gabija', 'Kajus', 'Austėja', 'Rokas', 'Ieva', 'Tomas', 'Urtė', 'Dominykas', 'Kotryna'],
    lastNames: ['Kazlauskas', 'Petrauskas', 'Jankauskas', 'Stankevičius', 'Vasiliauskas', 'Žukauskas', 'Butkus', 'Paulauskas', 'Urbonas', 'Kavaliauskas', 'Rimkus', 'Laurinavičius', 'Mačiulis', 'Gudas', 'Navickas', 'Šimkus']
  },
  'lv_LV': {
    firstNames: ['Roberts', 'Sofija', 'Artūrs', 'Anna', 'Emīls', 'Elizabete', 'Markus', 'Marija', 'Aleksis', 'Viktōrija', 'Daniels', 'Katrīna', 'Rihards', 'Līva', 'Kristaps', 'Elīza'],
    lastNames: ['Bērziņš', 'Kalniņš', 'Liepiņš', 'Ozols', 'Krūmiņš', 'Zariņš', 'Pētersons', 'Jansons', 'Kļaviņš', 'Rozītis', 'Sproģis', 'Grants', 'Freibergs', 'Vītoliņš', 'Āboliņš', 'Dumpis']
  },
  'et_EE': {
    firstNames: ['Rasmus', 'Emma', 'Oliver', 'Sofia', 'Robin', 'Maria', 'Hugo', 'Anna', 'Mattias', 'Mia', 'Sebastian', 'Laura', 'Lucas', 'Lisette', 'Kevin', 'Hanna'],
    lastNames: ['Tamm', 'Saar', 'Sepp', 'Mägi', 'Kask', 'Kukk', 'Rebane', 'Ilves', 'Pärn', 'Koppel', 'Vask', 'Roos', 'Känd', 'Org', 'Kuusk', 'Laur']
  },
  'uk_UA': {
    firstNames: ['Олександр', 'Анна', 'Максим', 'Марія', 'Артем', 'Олена', 'Дмитро', 'Катерина', 'Андрій', 'Ірина', 'Сергій', 'Наталія', 'Володимир', 'Тетяна', 'Михайло', 'Оксана'],
    lastNames: ['Мельник', 'Шевченко', 'Бойко', 'Коваленко', 'Бондаренко', 'Ткаченко', 'Кравченко', 'Олійник', 'Шевчук', 'Поліщук', 'Лисенко', 'Гриценко', 'Руденко', 'Савченко', 'Петренко', 'Іваненко']
  },
  'el_GR': {
    firstNames: ['Γιάννης', 'Μαρία', 'Γιώργος', 'Ελένη', 'Δημήτρης', 'Κατερίνα', 'Νίκος', 'Άννα', 'Κώστας', 'Σοφία', 'Παναγιώτης', 'Βασιλική', 'Αντώνης', 'Χριστίνα', 'Μιχάλης', 'Ιωάννα'],
    lastNames: ['Παπαδόπουλος', 'Γεωργίου', 'Παπαγεωργίου', 'Δημητρίου', 'Κωνσταντίνου', 'Νικολάου', 'Ιωάννου', 'Παπαδάκης', 'Αντωνίου', 'Μιχαήλ', 'Στεφάνου', 'Χριστοδούλου', 'Παπαδημητρίου', 'Αθανασίου', 'Βασιλείου', 'Γρηγορίου']
  },
  'el_CY': {
    firstNames: ['Ανδρέας', 'Μαρία', 'Γιώργος', 'Ελένη', 'Χρίστος', 'Άννα', 'Νίκος', 'Κατερίνα', 'Παναγιώτης', 'Σοφία', 'Μιχάλης', 'Χριστίνα', 'Κώστας', 'Ιωάννα', 'Αντώνης', 'Δέσποινα'],
    lastNames: ['Χαραλάμπους', 'Γεωργίου', 'Δημητρίου', 'Νικολάου', 'Ιωάννου', 'Κωνσταντίνου', 'Αντωνίου', 'Μιχαήλ', 'Στεφάνου', 'Χριστοδούλου', 'Αθανασίου', 'Βασιλείου', 'Γρηγορίου', 'Παπαδόπουλος', 'Σάββα', 'Λουκά']
  },
  'at_AT': {
    firstNames: ['Maximilian', 'Anna', 'Alexander', 'Laura', 'Paul', 'Lena', 'Elias', 'Emma', 'Jakob', 'Leonie', 'David', 'Sophie', 'Noah', 'Marie', 'Simon', 'Johanna'],
    lastNames: ['Gruber', 'Huber', 'Bauer', 'Wagner', 'Müller', 'Pichler', 'Steiner', 'Moser', 'Mayer', 'Hofer', 'Leitner', 'Berger', 'Fuchs', 'Eder', 'Fischer', 'Schmid']
  },

  // === 南美洲 ===
  'es_AR': {
    firstNames: ['Santiago', 'Emma', 'Mateo', 'Olivia', 'Benjamín', 'Martina', 'Nicolás', 'Sofia', 'Thiago', 'Isabella', 'Bautista', 'Mía', 'Lautaro', 'Catalina', 'Joaquín', 'Valentina'],
    lastNames: ['González', 'Rodríguez', 'Gómez', 'Fernández', 'López', 'Díaz', 'Martínez', 'Pérez', 'García', 'Martín', 'Sánchez', 'Romero', 'Ruiz', 'Vargas', 'Castro', 'Álvarez']
  },
  'es_PE': {
    firstNames: ['Sebastián', 'Sofía', 'Mateo', 'Valentina', 'Diego', 'Emma', 'Nicolás', 'Isabella', 'Adrián', 'Camila', 'Gabriel', 'Martina', 'Joaquín', 'Luciana', 'Santiago', 'Antonella'],
    lastNames: ['García', 'López', 'Rodríguez', 'Pérez', 'González', 'Sánchez', 'Ramírez', 'Torres', 'Flores', 'Rivera', 'Gómez', 'Díaz', 'Cruz', 'Morales', 'Ortiz', 'Gutiérrez']
  },
  'es_VE': {
    firstNames: ['Santiago', 'Sofía', 'Mateo', 'Valentina', 'Sebastián', 'Emma', 'Diego', 'Isabella', 'Nicolás', 'Camila', 'Gabriel', 'Martina', 'Adrián', 'Luciana', 'Joaquín', 'Antonella'],
    lastNames: ['García', 'Rodríguez', 'González', 'Hernández', 'López', 'Martínez', 'Pérez', 'Sánchez', 'Ramírez', 'Torres', 'Flores', 'Rivera', 'Gómez', 'Díaz', 'Morales', 'Gutiérrez']
  },  // === 非洲 ===
  'ar_EG': {
    firstNames: ['محمد', 'فاطمة', 'أحمد', 'عائشة', 'علي', 'خديجة', 'حسن', 'زينب', 'عمر', 'مريم', 'يوسف', 'أسماء', 'إبراهيم', 'نور', 'عبدالله', 'سارة'],
    lastNames: ['محمد', 'أحمد', 'علي', 'حسن', 'إبراهيم', 'عبدالله', 'محمود', 'عبدالرحمن', 'خالد', 'عمر', 'يوسف', 'السيد', 'عبدالعزيز', 'مصطفى', 'عثمان', 'صالح']
  },
  'en_ZA': {
    firstNames: ['Liam', 'Emma', 'Noah', 'Olivia', 'William', 'Ava', 'James', 'Isabella', 'Benjamin', 'Sophia', 'Lucas', 'Charlotte', 'Henry', 'Mia', 'Alexander', 'Amelia'],
    lastNames: ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Miller', 'Davis', 'Garcia', 'Rodriguez', 'Wilson', 'Martinez', 'Anderson', 'Taylor', 'Thomas', 'Hernandez', 'Moore']
  },
  'en_NG': {
    firstNames: ['Chinedu', 'Ngozi', 'Emeka', 'Adaeze', 'Kelechi', 'Chioma', 'Obinna', 'Ifeoma', 'Chukwudi', 'Amaka', 'Ikechukwu', 'Nneka', 'Chidi', 'Chiamaka', 'Nnamdi', 'Chinelo'],
    lastNames: ['Okafor', 'Okwu', 'Nwankwo', 'Eze', 'Okonkwo', 'Okoro', 'Nwosu', 'Anyanwu', 'Obi', 'Chukwu', 'Nwachukwu', 'Okeke', 'Ugwu', 'Nwokolo', 'Onwuegbuzie', 'Okafor']
  },
  'en_UG': {
    firstNames: ['John', 'Mary', 'Paul', 'Sarah', 'David', 'Grace', 'Peter', 'Ruth', 'James', 'Rebecca', 'Samuel', 'Esther', 'Joseph', 'Rachel', 'Daniel', 'Miriam'],
    lastNames: ['Mukasa', 'Namukasa', 'Ssali', 'Nakato', 'Kato', 'Nakamya', 'Ssekandi', 'Namatovu', 'Wasswa', 'Nabirye', 'Kiprotich', 'Akello', 'Okello', 'Auma', 'Ochieng', 'Atieno']
  },

  // === 大洋洲 ===
  'en_AU': {
    firstNames: ['William', 'Charlotte', 'Oliver', 'Olivia', 'Jack', 'Amelia', 'Henry', 'Isla', 'Lucas', 'Mia', 'Thomas', 'Grace', 'Ethan', 'Zoe', 'James', 'Sophie'],
    lastNames: ['Smith', 'Jones', 'Williams', 'Brown', 'Wilson', 'Taylor', 'Johnson', 'White', 'Martin', 'Anderson', 'Thompson', 'Nguyen', 'Thomas', 'Walker', 'Harris', 'Lee']
  },
  'en_NZ': {
    firstNames: ['Oliver', 'Charlotte', 'Jack', 'Amelia', 'William', 'Isla', 'James', 'Olivia', 'Noah', 'Emily', 'Lucas', 'Mia', 'Henry', 'Grace', 'Leo', 'Zoe'],
    lastNames: ['Smith', 'Brown', 'Wilson', 'Taylor', 'Anderson', 'Thomas', 'Jackson', 'White', 'Harris', 'Martin', 'Thompson', 'Garcia', 'Martinez', 'Robinson', 'Clark', 'Rodriguez']
  },

  // === 中东 ===
  'ar_SA': {
    firstNames: ['محمد', 'فاطمة', 'عبدالله', 'عائشة', 'أحمد', 'خديجة', 'علي', 'زينب', 'عمر', 'مريم', 'حسن', 'أسماء', 'يوسف', 'نور', 'إبراهيم', 'سارة'],
    lastNames: ['آل سعود', 'العتيبي', 'الغامدي', 'القحطاني', 'الحربي', 'المطيري', 'العنزي', 'الدوسري', 'الشهري', 'الزهراني', 'الخالدي', 'العسيري', 'الرشيد', 'البقمي', 'الجهني', 'السبيعي']
  },
  'ar_JO': {
    firstNames: ['محمد', 'فاطمة', 'أحمد', 'عائشة', 'علي', 'خديجة', 'حسن', 'زينب', 'عمر', 'مريم', 'يوسف', 'أسماء', 'إبراهيم', 'نور', 'عبدالله', 'سارة'],
    lastNames: ['الأردني', 'العبدالله', 'الخوري', 'النجار', 'الحداد', 'الطويل', 'القاسم', 'الشامي', 'البدوي', 'الفلسطيني', 'اللبناني', 'السوري', 'المصري', 'العراقي', 'اليمني', 'السعودي']
  }
};

// 地址数据库 - 按照国际标准7行格式
const ADDRESS_DATABASE: Record<string, {
  cities: string[],
  streets: string[],
  postalCodes: string[],
  states?: string[],
  districts: string[], // 区/县/地区
  format: 'standard' | 'reverse' // 地址格式：标准(小到大) 或 反向(大到小)
}> = {
  // 中国 - 格式：姓名/邮编/省市/街道/区县/国家/邮箱
  'zh_CN': {
    cities: ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市', '西安市', '重庆市', '天津市', '苏州市', '长沙市', '郑州市', '青岛市'],
    states: ['北京', '上海', '广东省', '广东省', '浙江省', '江苏省', '湖北省', '四川省', '陕西省', '重庆', '天津', '江苏省', '湖南省', '河南省', '山东省'],
    districts: ['朝阳区', '浦东新区', '天河区', '南山区', '西湖区', '鼓楼区', '武昌区', '锦江区', '雁塔区', '渝中区', '和平区', '姑苏区', '岳麓区', '金水区', '市南区'],
    streets: ['中山路', '人民路', '解放路', '建设路', '和平路', '胜利路', '文化路', '新华路', '光明路', '友谊路', '团结路', '幸福路', '民主路', '自由路', '繁荣路'],
    postalCodes: ['100000', '200000', '510000', '518000', '310000', '210000', '430000', '610000', '710000', '400000', '300000', '215000', '410000', '450000', '266000'],
    format: 'reverse' // 中国地址格式：大到小
  },
  
  // 台湾
  'zh_TW': {
    cities: ['台北市', '高雄市', '台中市', '台南市', '桃園市', '新竹市', '基隆市', '嘉義市', '台東縣', '花蓮縣', '宜蘭縣', '苗栗縣', '彰化縣', '南投縣', '雲林縣'],
    states: ['台北', '高雄', '台中', '台南', '桃園', '新竹', '基隆', '嘉義', '台東', '花蓮', '宜蘭', '苗栗', '彰化', '南投', '雲林'],
    districts: ['中正區', '前金區', '西區', '中西區', '桃園區', '東區', '仁愛區', '西區', '台東市', '花蓮市', '宜蘭市', '苗栗市', '彰化市', '南投市', '斗六市'],
    streets: ['中山路', '中正路', '民生路', '忠孝路', '仁愛路', '信義路', '和平路', '復興路', '建國路', '民權路', '光復路', '中華路', '成功路', '自由路', '博愛路'],
    postalCodes: ['100', '800', '400', '700', '330', '300', '200', '600', '950', '970', '260', '350', '500', '540', '630'],
    format: 'reverse'
  },

  // 日本 - 格式：姓名/邮编/都道府县/市区町村/街道/国家/邮箱
  'ja_JP': {
    cities: ['東京都', '大阪市', '横浜市', '名古屋市', '札幌市', '神戸市', '京都市', '福岡市', '川崎市', 'さいたま市', '広島市', '仙台市', '北九州市', '千葉市', '世田谷区'],
    states: ['東京都', '大阪府', '神奈川県', '愛知県', '北海道', '兵庫県', '京都府', '福岡県', '神奈川県', '埼玉県', '広島県', '宮城県', '福岡県', '千葉県', '東京都'],
    districts: ['千代田区', '中央区', '中区', '中区', '中央区', '中央区', '中京区', '中央区', '川崎区', '浦和区', '中区', '青葉区', '小倉北区', '中央区', '世田谷区'],
    streets: ['中央', '本町', '大手町', '丸の内', '銀座', '新宿', '渋谷', '池袋', '上野', '浅草', '品川', '六本木', '赤坂', '青山', '表参道'],
    postalCodes: ['100-0001', '530-0001', '220-0001', '460-0001', '060-0001', '650-0001', '600-0001', '810-0001', '210-0001', '330-0001', '730-0001', '980-0001', '802-0001', '260-0001', '154-0001'],
    format: 'reverse'
  },

  // 韩国
  'ko_KR': {
    cities: ['서울시', '부산시', '대구시', '인천시', '광주시', '대전시', '울산시', '수원시', '고양시', '용인시', '성남시', '청주시', '안산시', '전주시', '천안시'],
    states: ['서울특별시', '부산광역시', '대구광역시', '인천광역시', '광주광역시', '대전광역시', '울산광역시', '경기도', '경기도', '경기도', '경기도', '충청북도', '경기도', '전라북도', '충청남도'],
    districts: ['강남구', '해운대구', '중구', '남동구', '서구', '유성구', '남구', '영통구', '덕양구', '기흥구', '분당구', '상당구', '단원구', '완산구', '동남구'],
    streets: ['강남대로', '테헤란로', '종로', '명동길', '홍대입구', '이태원로', '압구정로', '신촌로', '건대입구', '노원로', '송파대로', '영등포로', '마포대로', '서초대로', '잠실로'],
    postalCodes: ['06292', '48058', '41566', '22101', '61475', '34126', '44919', '16499', '10408', '16827', '13494', '28644', '15588', '54999', '31116'],
    format: 'reverse'
  },
  
  // 美国 - 格式：姓名/街道/城市/州/邮编/国家/邮箱
  'en_US': {
    cities: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville', 'Fort Worth', 'Columbus', 'Charlotte'],
    states: ['NY', 'CA', 'IL', 'TX', 'AZ', 'PA', 'TX', 'CA', 'TX', 'CA', 'TX', 'FL', 'TX', 'OH', 'NC'],
    districts: ['Manhattan', 'Hollywood', 'Downtown', 'Downtown', 'Central City', 'Center City', 'Downtown', 'Downtown', 'Downtown', 'Downtown', 'Downtown', 'Downtown', 'Downtown', 'Downtown', 'Uptown'],
    streets: ['Main St', 'First St', 'Second St', 'Park Ave', 'Oak St', 'Pine St', 'Maple St', 'Cedar St', 'Elm St', 'Washington St', 'Lake St', 'Hill St', 'Church St', 'Spring St', 'Center St'],
    postalCodes: ['10001', '90210', '60601', '77001', '85001', '19101', '78201', '92101', '75201', '95101', '73301', '32099', '76101', '43085', '28201'],
    format: 'standard'
  },
  
  // 英国 - 格式：姓名/街道/城市/郡/邮编/国家/邮箱
  'en_GB': {
    cities: ['London', 'Birmingham', 'Manchester', 'Glasgow', 'Liverpool', 'Leeds', 'Sheffield', 'Edinburgh', 'Bristol', 'Cardiff', 'Leicester', 'Coventry', 'Bradford', 'Belfast', 'Nottingham'],
    states: ['Greater London', 'West Midlands', 'Greater Manchester', 'Glasgow City', 'Merseyside', 'West Yorkshire', 'South Yorkshire', 'City of Edinburgh', 'Bristol', 'Cardiff', 'Leicestershire', 'West Midlands', 'West Yorkshire', 'Belfast', 'Nottinghamshire'],
    districts: ['Westminster', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'Old Town', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'City Centre'],
    streets: ['High Street', 'Church Lane', 'Main Street', 'Park Road', 'Victoria Road', 'Green Lane', 'Manor Road', 'Church Street', 'Park Lane', 'Kings Road', 'Queens Road', 'Mill Lane', 'School Lane', 'New Road', 'Elm Grove'],
    postalCodes: ['SW1A 1AA', 'B1 1AA', 'M1 1AA', 'G1 1AA', 'L1 8JQ', 'LS1 1UR', 'S1 2HE', 'EH1 1YZ', 'BS1 4DJ', 'CF10 1BH', 'LE1 6ZG', 'CV1 1GF', 'BD1 1DB', 'BT1 1AA', 'NG1 1AA'],
    format: 'standard'
  },

  // 德国 - 格式：姓名/街道/邮编/城市/州/国家/邮箱
  'de_DE': {
    cities: ['Berlin', 'Hamburg', 'München', 'Köln', 'Frankfurt', 'Stuttgart', 'Düsseldorf', 'Dortmund', 'Essen', 'Leipzig', 'Bremen', 'Dresden', 'Hannover', 'Nürnberg', 'Duisburg'],
    states: ['Berlin', 'Hamburg', 'Bayern', 'Nordrhein-Westfalen', 'Hessen', 'Baden-Württemberg', 'Nordrhein-Westfalen', 'Nordrhein-Westfalen', 'Nordrhein-Westfalen', 'Sachsen', 'Bremen', 'Sachsen', 'Niedersachsen', 'Bayern', 'Nordrhein-Westfalen'],
    districts: ['Mitte', 'Altstadt', 'Altstadt', 'Altstadt', 'Innenstadt', 'Mitte', 'Altstadt', 'Innenstadt', 'Stadtmitte', 'Zentrum', 'Altstadt', 'Altstadt', 'Mitte', 'Altstadt', 'Altstadt'],
    streets: ['Hauptstraße', 'Schulstraße', 'Dorfstraße', 'Bahnhofstraße', 'Kirchstraße', 'Gartenstraße', 'Mühlenstraße', 'Lindenstraße', 'Bergstraße', 'Poststraße', 'Marktstraße', 'Friedhofstraße', 'Ringstraße', 'Waldstraße', 'Am Markt'],
    postalCodes: ['10115', '20095', '80331', '50667', '60311', '70173', '40213', '44135', '45127', '04109', '28195', '01067', '30159', '90402', '47051'],
    format: 'standard'
  },

  // 法国 - 格式：姓名/街道/邮编/城市/大区/国家/邮箱
  'fr_FR': {
    cities: ['Paris', 'Marseille', 'Lyon', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg', 'Montpellier', 'Bordeaux', 'Lille', 'Rennes', 'Reims', 'Le Havre', 'Saint-Étienne', 'Toulon'],
    states: ['Île-de-France', 'Provence-Alpes-Côte d\'Azur', 'Auvergne-Rhône-Alpes', 'Occitanie', 'Provence-Alpes-Côte d\'Azur', 'Pays de la Loire', 'Grand Est', 'Occitanie', 'Nouvelle-Aquitaine', 'Hauts-de-France', 'Bretagne', 'Grand Est', 'Normandie', 'Auvergne-Rhône-Alpes', 'Provence-Alpes-Côte d\'Azur'],
    districts: ['1er arrondissement', '1er arrondissement', '1er arrondissement', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre'],
    streets: ['Rue de la Paix', 'Avenue des Champs-Élysées', 'Rue de Rivoli', 'Boulevard Saint-Germain', 'Rue du Faubourg Saint-Honoré', 'Avenue Montaigne', 'Rue Saint-Antoine', 'Boulevard Haussmann', 'Rue de la République', 'Avenue de la Liberté', 'Rue Victor Hugo', 'Place de la République', 'Rue Nationale', 'Avenue Jean Jaurès', 'Rue Gambetta'],
    postalCodes: ['75001', '13001', '69001', '31000', '06000', '44000', '67000', '34000', '33000', '59000', '35000', '51100', '76600', '42000', '83000'],
    format: 'standard'
  },

  // 印度 - 格式：姓名/街道/城市/州/邮编/国家/邮箱
  'en_IN': {
    cities: ['Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata', 'Pune', 'Ahmedabad', 'Jaipur', 'Surat', 'Lucknow', 'Kanpur', 'Nagpur', 'Indore', 'Thane'],
    states: ['Maharashtra', 'Delhi', 'Karnataka', 'Telangana', 'Tamil Nadu', 'West Bengal', 'Maharashtra', 'Gujarat', 'Rajasthan', 'Gujarat', 'Uttar Pradesh', 'Uttar Pradesh', 'Maharashtra', 'Madhya Pradesh', 'Maharashtra'],
    districts: ['Andheri', 'Connaught Place', 'Koramangala', 'Banjara Hills', 'T. Nagar', 'Salt Lake', 'Koregaon Park', 'Satellite', 'C-Scheme', 'Adajan', 'Hazratganj', 'Civil Lines', 'Sadar', 'Vijay Nagar', 'Hiranandani'],
    streets: ['MG Road', 'Brigade Road', 'Commercial Street', 'Park Street', 'Linking Road', 'FC Road', 'SG Highway', 'MI Road', 'Ring Road', 'Mall Road', 'Station Road', 'Civil Lines', 'Residency Road', 'AB Road', 'Eastern Express Highway'],
    postalCodes: ['400001', '110001', '560001', '500001', '600001', '700001', '411001', '380001', '302001', '395001', '226001', '208001', '440001', '452001', '400601'],
    format: 'standard'
  },

  // 泰国 - 格式：姓名/街道/城市/府/邮编/国家/邮箱
  'th_TH': {
    cities: ['กรุงเทพมหานคร', 'เชียงใหม่', 'นครราชสีมา', 'ขอนแก่น', 'อุดรธานี', 'สุราษฎร์ธานี', 'ชลบุรี', 'นครศรีธรรมราช', 'เชียงราย', 'ลำปาง', 'อุบลราชธานี', 'ร้อยเอ็ด', 'สกลนคร', 'นครสวรรค์', 'ศรีสะเกษ'],
    states: ['กรุงเทพมหานคร', 'เชียงใหม่', 'นครราชสีมา', 'ขอนแก่น', 'อุดรธานี', 'สุราษฎร์ธานี', 'ชลบุรี', 'นครศรีธรรมราช', 'เชียงราย', 'ลำปาง', 'อุบลราชธานี', 'ร้อยเอ็ด', 'สกลนคร', 'นครสวรรค์', 'ศรีสะเกษ'],
    districts: ['วัฒนา', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง'],
    streets: ['ถนนสุขุมวิท', 'ถนนพหลโยธิน', 'ถนนรัชดาภิเษก', 'ถนนเพชรบุรี', 'ถนนสีลม', 'ถนนสาทร', 'ถนนวิภาวดีรังสิต', 'ถนนลาดพร้าว', 'ถนนรามคำแหง', 'ถนนบางนา', 'ถนนเจริญกรุง', 'ถนนราชดำริ', 'ถนนปลวกแดง', 'ถนนมิตรภาพ', 'ถนนอโศก'],
    postalCodes: ['10110', '50000', '30000', '40000', '41000', '84000', '20000', '80000', '57000', '52000', '34000', '45000', '47000', '60000', '33000'],
    format: 'reverse'
  },

  // 越南 - 格式：姓名/街道/区/城市/省/邮编/国家/邮箱
  'vi_VN': {
    cities: ['TP. Hồ Chí Minh', 'Hà Nội', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ', 'Biên Hòa', 'Huế', 'Nha Trang', 'Buôn Ma Thuột', 'Vũng Tàu', 'Quy Nhon', 'Nam Định', 'Long Xuyên', 'Rạch Giá', 'Thái Nguyên'],
    states: ['TP. Hồ Chí Minh', 'Hà Nội', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ', 'Đồng Nai', 'Thừa Thiên Huế', 'Khánh Hòa', 'Đắk Lắk', 'Bà Rịa-Vũng Tàu', 'Bình Định', 'Nam Định', 'An Giang', 'Kiên Giang', 'Thái Nguyên'],
    districts: ['Quận 1', 'Quận 3', 'Quận 5', 'Ba Đình', 'Hoàn Kiếm', 'Hai Châu', 'Hồng Bàng', 'Ninh Kiều', 'Biên Hòa', 'Huế', 'Nha Trang', 'Buôn Ma Thuột', 'Vũng Tàu', 'Quy Nhon', 'Nam Định'],
    streets: ['Nguyễn Trãi', 'Lê Lợi', 'Trần Hưng Đạo', 'Hai Bà Trưng', 'Điện Biên Phủ', 'Võ Văn Kiệt', 'Cách Mạng Tháng 8', 'Nguyễn Huệ', 'Đồng Khởi', 'Nam Kỳ Khởi Nghĩa', 'Pasteur', 'Lý Tự Trọng', 'Nguyễn Thị Minh Khai', 'Phạm Ngũ Lão', 'Bùi Viện'],
    postalCodes: ['700000', '100000', '550000', '180000', '900000', '810000', '530000', '650000', '630000', '790000', '590000', '420000', '880000', '920000', '250000'],
    format: 'standard'
  },

  // 马来西亚 - 格式：姓名/街道/邮编/城市/州/国家/邮箱
  'ms_MY': {
    cities: ['Kuala Lumpur', 'Selangor', 'Johor Bahru', 'Penang', 'Ipoh', 'Kuching', 'Kota Kinabalu', 'Shah Alam', 'Malacca', 'Alor Setar', 'Kuantan', 'Kota Bharu', 'Miri', 'Sandakan', 'Tawau'],
    states: ['Kuala Lumpur', 'Selangor', 'Johor', 'Penang', 'Perak', 'Sarawak', 'Sabah', 'Selangor', 'Malacca', 'Kedah', 'Pahang', 'Kelantan', 'Sarawak', 'Sabah', 'Sabah'],
    districts: ['KLCC', 'Petaling Jaya', 'Johor Bahru', 'Georgetown', 'Ipoh', 'Kuching', 'Kota Kinabalu', 'Shah Alam', 'Malacca', 'Alor Setar', 'Kuantan', 'Kota Bharu', 'Miri', 'Sandakan', 'Tawau'],
    streets: ['Jalan Bukit Bintang', 'Jalan Ampang', 'Jalan Raja Chulan', 'Jalan Tun Razak', 'Jalan Imbi', 'Jalan Pudu', 'Jalan Chow Kit', 'Jalan Tuanku Abdul Rahman', 'Jalan Masjid India', 'Jalan Petaling', 'Jalan Hang Tuah', 'Jalan Sultan Ismail', 'Jalan Dang Wangi', 'Jalan Davis', 'Jalan Tengku Abdul Rahman'],
    postalCodes: ['50000', '40000', '80000', '10000', '30000', '93000', '88000', '40000', '75000', '05000', '25000', '15000', '98000', '90000', '91000'],
    format: 'standard'
  },

  // 印尼 - 格式：姓名/街道/区/城市/邮编/省/国家/邮箱
  'id_ID': {
    cities: ['Jakarta', 'Surabaya', 'Bandung', 'Medan', 'Semarang', 'Makassar', 'Palembang', 'Tangerang', 'Depok', 'Bekasi', 'Padang', 'Denpasar', 'Malang', 'Samarinda', 'Banjarmasin'],
    states: ['DKI Jakarta', 'Jawa Timur', 'Jawa Barat', 'Sumatera Utara', 'Jawa Tengah', 'Sulawesi Selatan', 'Sumatera Selatan', 'Banten', 'Jawa Barat', 'Jawa Barat', 'Sumatera Barat', 'Bali', 'Jawa Timur', 'Kalimantan Timur', 'Kalimantan Selatan'],
    districts: ['Jakarta Pusat', 'Surabaya Pusat', 'Bandung Wetan', 'Medan Kota', 'Semarang Tengah', 'Makassar', 'Ilir Timur', 'Tangerang Kota', 'Depok', 'Bekasi Kota', 'Padang Barat', 'Denpasar Selatan', 'Klojen', 'Samarinda Kota', 'Banjarmasin Tengah'],
    streets: ['Jalan Thamrin', 'Jalan Sudirman', 'Jalan Gatot Subroto', 'Jalan Kuningan', 'Jalan Kemang', 'Jalan Senopati', 'Jalan Menteng', 'Jalan Cikini', 'Jalan Sabang', 'Jalan Jaksa', 'Jalan Malioboro', 'Jalan Asia Afrika', 'Jalan Braga', 'Jalan Dago', 'Jalan Setiabudhi'],
    postalCodes: ['10110', '60271', '40111', '20111', '50241', '90111', '30111', '15111', '16411', '17141', '25111', '80221', '65111', '75111', '70111'],
    format: 'standard'
  },

  // 香港 - 格式：姓名/单位/楼层/建筑/街道/区/香港/邮箱 (无邮编)
  'en_HK': {
    cities: ['Central', 'Wan Chai', 'Causeway Bay', 'Tsim Sha Tsui', 'Mong Kok', 'Yau Ma Tei', 'Jordan', 'Admiralty', 'Sheung Wan', 'Mid-Levels', 'Happy Valley', 'North Point', 'Quarry Bay', 'Tai Koo', 'Shau Kei Wan'],
    states: ['Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Kowloon', 'Kowloon', 'Kowloon', 'Kowloon', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island'],
    districts: ['Central', 'Wan Chai', 'Causeway Bay', 'Tsim Sha Tsui', 'Mong Kok', 'Yau Ma Tei', 'Jordan', 'Admiralty', 'Sheung Wan', 'Mid-Levels', 'Happy Valley', 'North Point', 'Quarry Bay', 'Tai Koo', 'Shau Kei Wan'],
    streets: ['Des Voeux Road', 'Queen\'s Road', 'Nathan Road', 'Canton Road', 'Hennessy Road', 'Lockhart Road', 'Gloucester Road', 'Connaught Road', 'Pedder Street', 'Ice House Street', 'Wellington Street', 'Hollywood Road', 'Caine Road', 'Robinson Road', 'Kennedy Road'],
    postalCodes: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // 香港不使用邮编
    format: 'standard'
  },

  // 新加坡 - 格式：姓名/街道/单位/建筑/新加坡/邮编/邮箱
  'en_SG': {
    cities: ['Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore'],
    states: ['Central', 'Orchard', 'Marina Bay', 'Chinatown', 'Little India', 'Bugis', 'Clarke Quay', 'Raffles Place', 'Tanjong Pagar', 'Boat Quay', 'Robertson Quay', 'Dhoby Ghaut', 'Somerset', 'City Hall', 'Esplanade'],
    districts: ['Central', 'Orchard', 'Marina Bay', 'Chinatown', 'Little India', 'Bugis', 'Clarke Quay', 'Raffles Place', 'Tanjong Pagar', 'Boat Quay', 'Robertson Quay', 'Dhoby Ghaut', 'Somerset', 'City Hall', 'Esplanade'],
    streets: ['Orchard Road', 'Marina Bay', 'Raffles Avenue', 'Shenton Way', 'Robinson Road', 'Cecil Street', 'Collyer Quay', 'Battery Road', 'Boat Quay', 'Clarke Quay', 'Robertson Quay', 'North Bridge Road', 'South Bridge Road', 'New Bridge Road', 'Eu Tong Sen Street'],
    postalCodes: ['238872', '018956', '039594', '048623', '068809', '179104', '179024', '048584', '068804', '049909', '238872', '179103', '228208', '179555', '039594'],
    format: 'standard'
  }
};// 随机选择函数
function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

// 生成随机数字
function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成随机邮箱
function generateEmail(firstName: string, lastName: string): string {
  const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'qq.com', '163.com', 'sina.com'];
  const cleanFirst = firstName.toLowerCase().replace(/[^a-z0-9]/g, '');
  const cleanLast = lastName.toLowerCase().replace(/[^a-z0-9]/g, '');
  const domain = getRandomItem(domains);
  
  const patterns = [
    `${cleanFirst}.${cleanLast}@${domain}`,
    `${cleanFirst}${cleanLast}@${domain}`,
    `${cleanFirst}${getRandomNumber(1, 999)}@${domain}`,
    `${cleanLast}${cleanFirst}@${domain}`
  ];
  
  return getRandomItem(patterns);
}

// 生成随机电话号码
function generatePhoneNumber(locale: string): string {
  const phoneFormats: Record<string, string[]> = {
    'zh_CN': ['+86 138-0013-8000', '+86 139-0013-9000', '+86 150-0015-0000'],
    'zh_TW': ['+886 912-345-678', '+886 987-654-321', '+886 955-123-456'],
    'ja_JP': ['+81 90-1234-5678', '+81 80-9876-5432', '+81 70-1111-2222'],
    'ko_KR': ['+82 10-1234-5678', '+82 11-9876-5432', '+82 16-1111-2222'],
    'en_US': ['+****************', '+****************', '+****************'],
    'en_GB': ['+44 20 7946 0958', '+44 ************', '+44 ************'],
    'de_DE': ['+49 30 12345678', '+49 40 87654321', '+49 89 11112222'],
    'fr_FR': ['+33 1 42 86 83 26', '+33 4 91 15 36 47', '+33 5 56 00 00 00']
  };
  
  const formats = phoneFormats[locale] || phoneFormats['en_US'];
  return getRandomItem(formats);
}

// 生成完整地址 - 按照7行标准格式
function generateAddress(locale: string): {
  address: string, // 完整的7行格式地址
  city: string,
  state: string,
  postalCode: string,
  country: string
} {
  const addressData = ADDRESS_DATABASE[locale];
  if (!addressData) {
    // 默认使用美国地址格式
    const usData = ADDRESS_DATABASE['en_US'];
    const street = `${getRandomNumber(1, 9999)} ${getRandomItem(usData.streets)}`;
    const city = getRandomItem(usData.cities);
    const state = getRandomItem(usData.states!);
    const district = getRandomItem(usData.districts);
    const postalCode = getRandomItem(usData.postalCodes);

    // 美国标准格式：街道/城市/州区合并
    const address = `${street}\n${city}\n${state} ${district}`;

    return {
      address,
      city,
      state,
      postalCode,
      country: 'United States'
    };
  }

  const street = `${getRandomNumber(1, 999)} ${getRandomItem(addressData.streets)}`;
  const city = getRandomItem(addressData.cities);
  const postalCode = getRandomItem(addressData.postalCodes);
  const state = addressData.states ? getRandomItem(addressData.states) : city;
  const district = getRandomItem(addressData.districts);

  // 根据locale确定国家名称
  const countryNames: Record<string, string> = {
    'zh_CN': '中国', 'zh_TW': '台湾', 'ja_JP': '日本', 'ko_KR': '韩国',
    'en_US': 'United States', 'en_GB': 'United Kingdom', 'en_CA': 'Canada', 'en_IN': 'India',
    'th_TH': 'Thailand', 'vi_VN': 'Vietnam', 'ms_MY': 'Malaysia', 'id_ID': 'Indonesia',
    'en_HK': 'Hong Kong', 'en_SG': 'Singapore',
    'de_DE': 'Germany', 'de_AT': 'Austria', 'de_CH': 'Switzerland',
    'fr_FR': 'France', 'fr_CA': 'Canada', 'fr_CH': 'Switzerland', 'fr_BE': 'Belgium',
    'it_IT': 'Italy', 'it_CH': 'Switzerland', 'es_ES': 'Spain',
    'nl_NL': 'Netherlands', 'nl_BE': 'Belgium', 'pt_PT': 'Portugal', 'pt_BR': 'Brazil',
    'ru_RU': 'Russia', 'sv_SE': 'Sweden', 'nb_NO': 'Norway', 'da_DK': 'Denmark',
    'fi_FI': 'Finland', 'is_IS': 'Iceland', 'pl_PL': 'Poland'
  };

  const country = countryNames[locale] || 'Unknown';

  // 根据地址格式生成严格的7行地址
  let address: string;
  if (addressData.format === 'reverse') {
    // 亚洲格式：大到小 (省市合并 + 街道 + 区)
    address = `${state}${city}\n${street}\n${district}`;
  } else {
    // 西方格式：小到大 (街道 + 城市 + 州区合并)
    address = `${street}\n${city}\n${state} ${district}`;
  }

  return {
    address,
    city,
    state,
    postalCode,
    country
  };
}

// 主要的本地生成函数
export function generateLocalBillToInfo(locale: string): BillToInfo {
  const nameData = NAME_DATABASE[locale];
  
  if (!nameData) {
    // 如果没有找到对应的locale，使用英文作为默认
    const defaultData = NAME_DATABASE['en_US'];
    const firstName = getRandomItem(defaultData.firstNames);
    const lastName = getRandomItem(defaultData.lastNames);
    const address = generateAddress('en_US');
    
    return {
      name: `${firstName} ${lastName}`,
      company: `${lastName} Corp`,
      email: generateEmail(firstName, lastName),
      phone: generatePhoneNumber('en_US'),
      address: address.address, // 使用完整的7行地址格式
      city: address.city,
      state: address.state,
      postalCode: address.postalCode,
      country: address.country
    };
  }
  
  const firstName = getRandomItem(nameData.firstNames);
  const lastName = getRandomItem(nameData.lastNames);
  const address = generateAddress(locale);
  
  return {
    name: `${firstName} ${lastName}`,
    company: `${lastName} Company`,
    email: generateEmail(firstName, lastName),
    phone: generatePhoneNumber(locale),
    address: address.address, // 使用完整的7行地址格式
    city: address.city,
    state: address.state,
    postalCode: address.postalCode,
    country: address.country
  };
}

// 检查是否支持某个locale
export function isLocaleSupported(locale: string): boolean {
  return locale in NAME_DATABASE;
}

// 获取所有支持的locale列表
export function getSupportedLocales(): string[] {
  return Object.keys(NAME_DATABASE);
}

// 测试函数 - 生成示例地址用于验证格式
export function testAddressGeneration() {
  console.log('=== 地址格式测试 ===');

  // 测试中国地址格式
  const cnAddress = generateAddress('zh_CN');
  console.log('中国地址格式:');
  console.log(`邮编: ${cnAddress.postalCode}`);
  console.log(`地址: ${cnAddress.address}`);
  console.log(`国家: ${cnAddress.country}`);
  console.log('---');

  // 测试美国地址格式
  const usAddress = generateAddress('en_US');
  console.log('美国地址格式:');
  console.log(`邮编: ${usAddress.postalCode}`);
  console.log(`地址: ${usAddress.address}`);
  console.log(`国家: ${usAddress.country}`);
  console.log('---');

  // 测试日本地址格式
  const jpAddress = generateAddress('ja_JP');
  console.log('日本地址格式:');
  console.log(`邮编: ${jpAddress.postalCode}`);
  console.log(`地址: ${jpAddress.address}`);
  console.log(`国家: ${jpAddress.country}`);
}