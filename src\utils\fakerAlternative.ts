// Faker API 备选方案 - 本地数据生成器
import { BillToInfo } from '@/types/invoice';
import { getAddressFormatByCountryCode, getLocaleByCountryCode } from './addressFormats';

// 姓名数据库 - 按语言/地区分类
const NAME_DATABASE = {
  // 中文姓名
  'zh_CN': {
    firstNames: ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀英', '霞', '平'],
    lastNames: ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
  },
  'zh_TW': {
    firstNames: ['志明', '春嬌', '美麗', '建國', '淑芬', '雅婷', '俊傑', '怡君', '宗翰', '佩君', '家豪', '雅雯', '承翰', '怡萱', '宗憲', '佳穎'],
    lastNames: ['陳', '林', '黃', '張', '李', '王', '吳', '劉', '蔡', '楊', '許', '鄭', '謝', '洪', '郭', '邱', '曾', '廖', '賴', '徐']
  },
  
  // 日文姓名
  'ja_JP': {
    firstNames: ['太郎', '花子', '一郎', '美咲', '翔太', '結衣', '大輔', '愛', '健太', '美穂', '拓也', '麻衣', '雄太', '恵', '慎一', '由美'],
    lastNames: ['田中', '佐藤', '鈴木', '高橋', '渡辺', '伊藤', '山本', '中村', '小林', '加藤', '吉田', '山田', '佐々木', '山口', '松本', '井上']
  },
  
  // 韩文姓名
  'ko_KR': {
    firstNames: ['민수', '지영', '현우', '수진', '준호', '미영', '성민', '혜진', '동현', '은지', '재현', '소영', '우진', '예은', '태현', '다은'],
    lastNames: ['김', '이', '박', '최', '정', '강', '조', '윤', '장', '임', '한', '오', '서', '신', '권', '황', '안', '송', '류', '전']
  },
  
  // 英文姓名
  'en_US': {
    firstNames: ['James', 'Mary', 'John', 'Patricia', 'Robert', 'Jennifer', 'Michael', 'Linda', 'William', 'Elizabeth', 'David', 'Barbara', 'Richard', 'Susan', 'Joseph', 'Jessica'],
    lastNames: ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas']
  },
  'en_GB': {
    firstNames: ['Oliver', 'Amelia', 'George', 'Isla', 'Harry', 'Ava', 'Noah', 'Mia', 'Jack', 'Isabella', 'Jacob', 'Sophia', 'Leo', 'Grace', 'Oscar', 'Lily'],
    lastNames: ['Smith', 'Jones', 'Taylor', 'Williams', 'Brown', 'Davies', 'Evans', 'Wilson', 'Thomas', 'Roberts', 'Johnson', 'Lewis', 'Walker', 'Robinson', 'Wood', 'Thompson']
  },
  'en_AU': {
    firstNames: ['William', 'Charlotte', 'Oliver', 'Olivia', 'Jack', 'Amelia', 'Henry', 'Isla', 'Lucas', 'Mia', 'Thomas', 'Grace', 'Ethan', 'Zoe', 'James', 'Sophie'],
    lastNames: ['Smith', 'Jones', 'Williams', 'Brown', 'Wilson', 'Taylor', 'Johnson', 'White', 'Martin', 'Anderson', 'Thompson', 'Nguyen', 'Thomas', 'Walker', 'Harris', 'Lee']
  },
  'en_CA': {
    firstNames: ['Liam', 'Emma', 'Noah', 'Olivia', 'William', 'Ava', 'James', 'Isabella', 'Benjamin', 'Sophia', 'Lucas', 'Charlotte', 'Henry', 'Mia', 'Alexander', 'Amelia'],
    lastNames: ['Smith', 'Brown', 'Tremblay', 'Martin', 'Roy', 'Wilson', 'MacDonald', 'Johnson', 'Taylor', 'Anderson', 'Campbell', 'Lee', 'White', 'Thompson', 'Moore', 'Young']
  },
  
  // 法文姓名
  'fr_FR': {
    firstNames: ['Louis', 'Emma', 'Gabriel', 'Jade', 'Raphaël', 'Louise', 'Arthur', 'Alice', 'Lucas', 'Chloé', 'Adam', 'Lina', 'Hugo', 'Rose', 'Maël', 'Anna'],
    lastNames: ['Martin', 'Bernard', 'Thomas', 'Petit', 'Robert', 'Richard', 'Durand', 'Dubois', 'Moreau', 'Laurent', 'Simon', 'Michel', 'Lefebvre', 'Leroy', 'Roux', 'David']
  },
  'fr_CA': {
    firstNames: ['Félix', 'Emma', 'William', 'Olivia', 'Liam', 'Charlotte', 'Noah', 'Alice', 'Thomas', 'Béatrice', 'Jacob', 'Rosalie', 'Raphaël', 'Juliette', 'Antoine', 'Zoé'],
    lastNames: ['Tremblay', 'Gagnon', 'Roy', 'Côté', 'Bouchard', 'Gauthier', 'Morin', 'Lavoie', 'Fortin', 'Gagné', 'Ouellet', 'Pelletier', 'Bélanger', 'Lévesque', 'Bergeron', 'Leblanc']
  },
  
  // 德文姓名
  'de_DE': {
    firstNames: ['Ben', 'Emma', 'Paul', 'Mia', 'Leon', 'Hannah', 'Finn', 'Sofia', 'Noah', 'Emilia', 'Louis', 'Lina', 'Henry', 'Marie', 'Felix', 'Lea'],
    lastNames: ['Müller', 'Schmidt', 'Schneider', 'Fischer', 'Weber', 'Meyer', 'Wagner', 'Becker', 'Schulz', 'Hoffmann', 'Schäfer', 'Koch', 'Bauer', 'Richter', 'Klein', 'Wolf']
  },
  'de_AT': {
    firstNames: ['Maximilian', 'Anna', 'Alexander', 'Laura', 'Paul', 'Lena', 'Elias', 'Emma', 'Jakob', 'Leonie', 'David', 'Sophie', 'Noah', 'Marie', 'Simon', 'Johanna'],
    lastNames: ['Gruber', 'Huber', 'Bauer', 'Wagner', 'Müller', 'Pichler', 'Steiner', 'Moser', 'Mayer', 'Hofer', 'Leitner', 'Berger', 'Fuchs', 'Eder', 'Fischer', 'Schmid']
  },
  
  // 西班牙文姓名
  'es_ES': {
    firstNames: ['Hugo', 'Lucía', 'Martín', 'María', 'Daniel', 'Paula', 'Pablo', 'Daniela', 'Alejandro', 'Carla', 'Adrián', 'Sara', 'Álvaro', 'Carmen', 'Diego', 'Sofía'],
    lastNames: ['García', 'Rodríguez', 'González', 'Fernández', 'López', 'Martínez', 'Sánchez', 'Pérez', 'Gómez', 'Martín', 'Jiménez', 'Ruiz', 'Hernández', 'Díaz', 'Moreno', 'Muñoz']
  },
  'es_AR': {
    firstNames: ['Santiago', 'Emma', 'Mateo', 'Olivia', 'Benjamín', 'Martina', 'Nicolás', 'Sofia', 'Thiago', 'Isabella', 'Lautaro', 'Mía', 'Joaquín', 'Catalina', 'Bautista', 'Delfina'],
    lastNames: ['González', 'Rodríguez', 'Gómez', 'Fernández', 'López', 'Díaz', 'Martínez', 'Pérez', 'García', 'Martín', 'Sánchez', 'Romero', 'Sosa', 'Contreras', 'Silva', 'Mendoza']
  },
  
  // 意大利文姓名
  'it_IT': {
    firstNames: ['Leonardo', 'Sofia', 'Francesco', 'Giulia', 'Lorenzo', 'Aurora', 'Alessandro', 'Alice', 'Andrea', 'Ginevra', 'Mattia', 'Emma', 'Gabriele', 'Giorgia', 'Riccardo', 'Beatrice'],
    lastNames: ['Rossi', 'Russo', 'Ferrari', 'Esposito', 'Bianchi', 'Romano', 'Colombo', 'Ricci', 'Marino', 'Greco', 'Bruno', 'Gallo', 'Conti', 'De Luca', 'Mancini', 'Costa']
  },
  
  // 葡萄牙文姓名
  'pt_BR': {
    firstNames: ['Miguel', 'Alice', 'Arthur', 'Sofia', 'Bernardo', 'Helena', 'Heitor', 'Valentina', 'Davi', 'Laura', 'Lorenzo', 'Isabella', 'Théo', 'Manuela', 'Pedro', 'Júlia'],
    lastNames: ['Silva', 'Santos', 'Oliveira', 'Souza', 'Rodrigues', 'Ferreira', 'Alves', 'Pereira', 'Lima', 'Gomes', 'Ribeiro', 'Carvalho', 'Barbosa', 'Almeida', 'Nascimento', 'Araújo']
  },
  'pt_PT': {
    firstNames: ['João', 'Leonor', 'Rodrigo', 'Matilde', 'Francisco', 'Carolina', 'Afonso', 'Inês', 'Tomás', 'Mariana', 'Duarte', 'Beatriz', 'Santiago', 'Ana', 'Pedro', 'Lara'],
    lastNames: ['Silva', 'Santos', 'Ferreira', 'Pereira', 'Oliveira', 'Costa', 'Rodrigues', 'Martins', 'Jesus', 'Sousa', 'Fernandes', 'Gonçalves', 'Gomes', 'Lopes', 'Marques', 'Alves']
  },
  
  // 俄文姓名
  'ru_RU': {
    firstNames: ['Александр', 'Анна', 'Михаил', 'Елена', 'Максим', 'Ольга', 'Артём', 'Татьяна', 'Дмитрий', 'Наталья', 'Андрей', 'Ирина', 'Алексей', 'Светлана', 'Иван', 'Мария'],
    lastNames: ['Иванов', 'Смирнов', 'Кузнецов', 'Попов', 'Васильев', 'Петров', 'Соколов', 'Михайлов', 'Новиков', 'Фёдоров', 'Морозов', 'Волков', 'Алексеев', 'Лебедев', 'Семёнов', 'Егоров']
  },
  
  // 阿拉伯文姓名
  'ar_SA': {
    firstNames: ['محمد', 'فاطمة', 'أحمد', 'عائشة', 'علي', 'خديجة', 'حسن', 'زينب', 'عبدالله', 'مريم', 'إبراهيم', 'أسماء', 'عمر', 'نور', 'يوسف', 'سارة'],
    lastNames: ['العتيبي', 'الغامدي', 'القحطاني', 'الحربي', 'المطيري', 'الدوسري', 'الشهري', 'الزهراني', 'العنزي', 'الشمري', 'المالكي', 'العسيري', 'الجهني', 'الرشيد', 'الخالدي', 'السبيعي']
  },
  
  // 荷兰文姓名
  'nl_NL': {
    firstNames: ['Daan', 'Emma', 'Sem', 'Tess', 'Lucas', 'Sophie', 'Finn', 'Fenna', 'Levi', 'Zoë', 'Jesse', 'Isa', 'Milan', 'Noa', 'Bram', 'Mila'],
    lastNames: ['de Jong', 'Jansen', 'de Vries', 'van den Berg', 'van Dijk', 'Bakker', 'Janssen', 'Visser', 'Smit', 'Meijer', 'de Boer', 'Mulder', 'de Groot', 'Bos', 'Vos', 'Peters']
  },
  
  // 瑞典文姓名
  'sv_SE': {
    firstNames: ['William', 'Alice', 'Liam', 'Maja', 'Noah', 'Vera', 'Hugo', 'Alma', 'Oliver', 'Astrid', 'Oscar', 'Lilly', 'Lucas', 'Ella', 'Elias', 'Alicia'],
    lastNames: ['Andersson', 'Johansson', 'Karlsson', 'Nilsson', 'Eriksson', 'Larsson', 'Olsson', 'Persson', 'Svensson', 'Gustafsson', 'Pettersson', 'Jonsson', 'Jansson', 'Hansson', 'Bengtsson', 'Jönsson']
  },
  
  // 挪威文姓名
  'nb_NO': {
    firstNames: ['Jakob', 'Emma', 'Emil', 'Nora', 'Noah', 'Sofie', 'Oliver', 'Leah', 'William', 'Sara', 'Lucas', 'Ella', 'Filip', 'Maja', 'Liam', 'Ingrid'],
    lastNames: ['Hansen', 'Johansen', 'Olsen', 'Larsen', 'Andersen', 'Pedersen', 'Nilsen', 'Kristiansen', 'Jensen', 'Karlsen', 'Johnsen', 'Pettersen', 'Eriksen', 'Berg', 'Haugen', 'Hagen']
  },
  
  // 丹麦文姓名
  'da_DK': {
    firstNames: ['William', 'Emma', 'Oliver', 'Ida', 'Noah', 'Clara', 'Oscar', 'Laura', 'Lucas', 'Mathilde', 'Carl', 'Sofia', 'Victor', 'Agnes', 'Magnus', 'Alma'],
    lastNames: ['Nielsen', 'Jensen', 'Hansen', 'Pedersen', 'Andersen', 'Christensen', 'Larsen', 'Sørensen', 'Rasmussen', 'Jørgensen', 'Petersen', 'Madsen', 'Kristensen', 'Olsen', 'Thomsen', 'Christiansen']
  }
};

// 地址数据库 - 按国家分类
const ADDRESS_DATABASE = {
  'US': {
    streets: ['Main Street', 'Oak Avenue', 'Pine Road', 'Elm Street', 'Maple Drive', 'Cedar Lane', 'Park Avenue', 'First Street', 'Second Avenue', 'Broadway'],
    cities: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose'],
    states: ['California', 'New York', 'Texas', 'Florida', 'Illinois', 'Pennsylvania', 'Ohio', 'Georgia', 'North Carolina', 'Michigan'],
    postcodes: () => Math.floor(Math.random() * 90000) + 10000
  },
  'CA': {
    streets: ['Main Street', 'King Street', 'Queen Street', 'Yonge Street', 'Bay Street', 'College Street', 'Dundas Street', 'Bloor Street', 'University Avenue', 'Front Street'],
    cities: ['Toronto', 'Montreal', 'Vancouver', 'Calgary', 'Edmonton', 'Ottawa', 'Winnipeg', 'Quebec City', 'Hamilton', 'Kitchener'],
    states: ['Ontario', 'Quebec', 'British Columbia', 'Alberta', 'Manitoba', 'Saskatchewan', 'Nova Scotia', 'New Brunswick', 'Newfoundland and Labrador', 'Prince Edward Island'],
    postcodes: () => {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const digits = '0123456789';
      return letters[Math.floor(Math.random() * letters.length)] + 
             digits[Math.floor(Math.random() * digits.length)] + 
             letters[Math.floor(Math.random() * letters.length)] + ' ' +
             digits[Math.floor(Math.random() * digits.length)] + 
             letters[Math.floor(Math.random() * letters.length)] + 
             digits[Math.floor(Math.random() * digits.length)];
    }
  },
  'GB': {
    streets: ['High Street', 'Church Lane', 'Victoria Road', 'The Green', 'Manor Road', 'School Lane', 'Queens Road', 'New Road', 'Mill Lane', 'Kings Road'],
    cities: ['London', 'Birmingham', 'Manchester', 'Glasgow', 'Liverpool', 'Leeds', 'Sheffield', 'Edinburgh', 'Bristol', 'Cardiff'],
    states: ['England', 'Scotland', 'Wales', 'Northern Ireland'],
    postcodes: () => {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const digits = '0123456789';
      return letters[Math.floor(Math.random() * letters.length)] + 
             letters[Math.floor(Math.random() * letters.length)] + 
             digits[Math.floor(Math.random() * digits.length)] + ' ' +
             digits[Math.floor(Math.random() * digits.length)] + 
             letters[Math.floor(Math.random() * letters.length)] + 
             letters[Math.floor(Math.random() * letters.length)];
    }
  },
  'AU': {
    streets: ['George Street', 'King Street', 'Queen Street', 'Collins Street', 'Bourke Street', 'Flinders Street', 'Elizabeth Street', 'Swanston Street', 'Pitt Street', 'Castlereagh Street'],
    cities: ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide', 'Gold Coast', 'Newcastle', 'Canberra', 'Sunshine Coast', 'Wollongong'],
    states: ['New South Wales', 'Victoria', 'Queensland', 'Western Australia', 'South Australia', 'Tasmania', 'Northern Territory', 'Australian Capital Territory'],
    postcodes: () => Math.floor(Math.random() * 9000) + 1000
  },
  'DE': {
    streets: ['Hauptstraße', 'Bahnhofstraße', 'Dorfstraße', 'Schulstraße', 'Gartenstraße', 'Kirchstraße', 'Mühlenstraße', 'Lindenstraße', 'Bergstraße', 'Poststraße'],
    cities: ['Berlin', 'Hamburg', 'München', 'Köln', 'Frankfurt am Main', 'Stuttgart', 'Düsseldorf', 'Dortmund', 'Essen', 'Leipzig'],
    states: ['Bayern', 'Baden-Württemberg', 'Nordrhein-Westfalen', 'Hessen', 'Sachsen', 'Niedersachsen', 'Berlin', 'Rheinland-Pfalz', 'Schleswig-Holstein', 'Brandenburg'],
    postcodes: () => Math.floor(Math.random() * 90000) + 10000
  },
  'FR': {
    streets: ['Rue de la Paix', 'Avenue des Champs-Élysées', 'Rue de Rivoli', 'Boulevard Saint-Germain', 'Rue du Faubourg Saint-Honoré', 'Avenue Montaigne', 'Rue Saint-Antoine', 'Boulevard Haussmann', 'Rue de la République', 'Avenue Victor Hugo'],
    cities: ['Paris', 'Marseille', 'Lyon', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg', 'Montpellier', 'Bordeaux', 'Lille'],
    states: ['Île-de-France', 'Auvergne-Rhône-Alpes', 'Nouvelle-Aquitaine', 'Occitanie', 'Hauts-de-France', 'Grand Est', 'Provence-Alpes-Côte d\'Azur', 'Pays de la Loire', 'Bretagne', 'Normandie'],
    postcodes: () => Math.floor(Math.random() * 90000) + 10000
  },
  'CN': {
    streets: ['中山路', '人民路', '解放路', '建设路', '和平路', '友谊路', '光明路', '胜利路', '文化路', '新华路'],
    cities: ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '南京', '天津', '重庆'],
    states: ['北京市', '上海市', '广东省', '浙江省', '江苏省', '山东省', '河南省', '四川省', '湖北省', '福建省'],
    postcodes: () => Math.floor(Math.random() * 900000) + 100000
  },
  'JP': {
    streets: ['中央通り', '本町通り', '駅前通り', '商店街', '大通り', '桜通り', '平和通り', '昭和通り', '新宿通り', '銀座通り'],
    cities: ['東京', '大阪', '横浜', '名古屋', '札幌', '神戸', '京都', '福岡', '川崎', '広島'],
    states: ['東京都', '大阪府', '神奈川県', '愛知県', '埼玉県', '千葉県', '兵庫県', '北海道', '福岡県', '静岡県'],
    postcodes: () => {
      const first = Math.floor(Math.random() * 900) + 100;
      const second = Math.floor(Math.random() * 9000) + 1000;
      return `${first}-${second}`;
    }
  }
};

// 随机选择数组中的元素
function randomChoice<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

// 生成随机姓名
function generateRandomName(locale: string): { firstName: string; lastName: string } {
  const nameData = NAME_DATABASE[locale as keyof typeof NAME_DATABASE] || NAME_DATABASE['en_US'];
  return {
    firstName: randomChoice(nameData.firstNames),
    lastName: randomChoice(nameData.lastNames)
  };
}

// 生成随机地址
function generateRandomAddress(countryCode: string): { street: string; city: string; state: string; postcode: string } {
  const addressData = ADDRESS_DATABASE[countryCode as keyof typeof ADDRESS_DATABASE] || ADDRESS_DATABASE['US'];
  return {
    street: randomChoice(addressData.streets),
    city: randomChoice(addressData.cities),
    state: randomChoice(addressData.states),
    postcode: typeof addressData.postcodes === 'function' ? addressData.postcodes().toString() : '12345'
  };
}

// 主要的备选生成函数
export async function generateBillToInfoAlternative(email: string, locale: string = 'en_US'): Promise<BillToInfo> {
  // 从locale推断国家代码
  const countryCode = locale.split('_')[1] || 'US';
  
  // 生成姓名
  const name = generateRandomName(locale);
  const fullName = locale.startsWith('zh_') ? `${name.lastName}${name.firstName}` : `${name.firstName} ${name.lastName}`;
  
  // 生成地址
  const address = generateRandomAddress(countryCode);
  const houseNumber = Math.floor(Math.random() * 9999) + 1;
  
  // 获取地址格式
  const addressFormat = getAddressFormatByCountryCode(countryCode);
  
  // 根据地址格式生成标准化地址
  if (addressFormat) {
    // 使用标准化格式
    const addressLine1 = `${houseNumber} ${address.street}`;
    const addressLine2 = `${address.city}, ${address.state}`;
    
    return {
      name: fullName.toUpperCase(),
      address1: address.postcode,
      address2: addressLine1,
      city: addressLine2,
      state: address.state,
      country: getCountryName(countryCode),
      email: email
    };
  } else {
    // 使用默认格式
    return {
      name: fullName.toUpperCase(),
      address1: address.postcode,
      address2: `${houseNumber} ${address.street}`,
      city: address.city,
      state: address.state,
      country: getCountryName(countryCode),
      email: email
    };
  }
}

// 获取国家名称
function getCountryName(countryCode: string): string {
  const countryNames: { [key: string]: string } = {
    'US': 'United States',
    'CA': 'Canada',
    'GB': 'United Kingdom',
    'AU': 'Australia',
    'DE': 'Germany',
    'FR': 'France',
    'CN': 'China',
    'JP': 'Japan',
    'KR': 'South Korea',
    'ES': 'Spain',
    'IT': 'Italy',
    'BR': 'Brazil',
    'RU': 'Russia',
    'IN': 'India',
    'MX': 'Mexico',
    'NL': 'Netherlands',
    'SE': 'Sweden',
    'NO': 'Norway',
    'DK': 'Denmark',
    'FI': 'Finland'
  };
  
  return countryNames[countryCode] || 'Unknown Country';
}

// 测试函数 - 生成多个样本
export async function generateMultipleSamples(count: number = 5, locale: string = 'en_US'): Promise<BillToInfo[]> {
  const samples: BillToInfo[] = [];
  
  for (let i = 0; i < count; i++) {
    const email = `test${i + 1}@example.com`;
    const sample = await generateBillToInfoAlternative(email, locale);
    samples.push(sample);
  }
  
  return samples;
}
