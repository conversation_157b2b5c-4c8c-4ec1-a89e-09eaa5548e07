# 🧹 PDF元数据清理工具使用说明

## 📋 功能介绍

此工具可以完全移除PDF文件中的所有元数据信息，包括：

- ✅ **Title** (标题)
- ✅ **Creator** (创建者 - 浏览器信息)
- ✅ **Producer** (生成器 - PDF引擎信息)
- ✅ **CreationDate** (创建日期)
- ✅ **ModDate** (修改日期)
- ✅ **Author** (作者)
- ✅ **Subject** (主题)
- ✅ **Keywords** (关键词)

## 🔄 PDF版本转换功能

支持转换PDF到以下版本：

- 📄 **PDF 1.3** - 兼容性最好，文件较大
- 📄 **PDF 1.4** - 支持透明度和加密
- 📄 **PDF 1.5** - 支持对象流压缩
- 📄 **PDF 1.6** - 支持3D内容和表单
- 📄 **PDF 1.7** - 最常用版本，功能完整
- 📄 **PDF 2.0** - 最新标准，功能最强

## 🚀 快速使用

### 方法1：一键启动（推荐）
1. 双击 `清理PDF.bat` 文件
2. 脚本会自动检查并安装所需依赖
3. 按照提示选择操作

### 方法2：手动运行
1. 确保已安装Python
2. 安装依赖：`pip install pypdf`
3. 运行：`python 清理PDF元数据.py`

## 📖 使用步骤

### 🔹 清理单个文件
1. 选择选项 `1`
2. 在弹出的文件选择对话框中选择PDF文件
3. **可选**：输入目标PDF版本 (1.3, 1.4, 1.5, 1.6, 1.7, 2.0)
4. 工具会自动生成 `原文件名_cleaned.pdf`
5. 完成后会显示成功提示

### 🔹 批量清理文件夹
1. 选择选项 `2`
2. 选择包含PDF文件的文件夹
3. **可选**：输入目标PDF版本进行批量转换
4. 工具会处理文件夹中的所有PDF文件
5. 每个文件都会生成对应的 `_cleaned.pdf` 版本

## 📁 文件说明

```
WIPDF/
├── 清理PDF元数据.py      # 主程序脚本
├── 清理PDF.bat           # Windows一键启动脚本
├── PDF元数据清理说明.md   # 使用说明文档
└── 快速启动.vbs          # 项目启动脚本
```

## ⚠️ 注意事项

1. **原文件保护**：工具不会修改原文件，而是生成新的清理版本
2. **文件命名**：清理后的文件会添加 `_cleaned` 后缀
3. **依赖要求**：需要Python环境和PyPDF2库
4. **兼容性**：支持所有标准PDF文件

## 🔧 系统要求

- **操作系统**：Windows 7/8/10/11
- **Python版本**：3.6 或更高版本
- **依赖库**：pypdf (最新版本)

## 💡 使用场景

- 🔒 **隐私保护**：移除PDF中的敏感元数据
- 📄 **文档分享**：清理后安全分享文档
- 🏢 **企业应用**：批量处理公司文档
- 🎯 **个人使用**：清理发票、收据等文件
- 🔄 **版本统一**：将不同版本的PDF统一为指定版本
- 📉 **兼容性优化**：转换为较低版本以提高兼容性

## 🆘 常见问题

### Q: 提示"未安装Python"怎么办？
A: 访问 https://www.python.org/downloads/ 下载并安装Python

### Q: pypdf安装失败怎么办？
A: 尝试使用管理员权限运行命令提示符，然后执行 `pip install pypdf`

### Q: 清理后的PDF文件大小会变化吗？
A: 文件大小基本不变，只是移除了元数据信息。版本转换可能会略微影响文件大小

### Q: 支持加密的PDF文件吗？
A: 不支持加密的PDF文件，需要先解密

### Q: 如何选择合适的PDF版本？
A:
- **PDF 1.4**: 适合需要兼容老旧软件的场景
- **PDF 1.7**: 推荐的通用版本，兼容性和功能平衡
- **PDF 2.0**: 最新功能，但可能不被老软件支持

### Q: 版本转换会影响PDF内容吗？
A: 不会影响可见内容，只是改变PDF的内部结构和兼容性

## 📞 技术支持

如果遇到问题，请检查：
1. Python是否正确安装
2. pypdf库是否安装成功
3. PDF文件是否损坏或加密
4. 是否有足够的磁盘空间

---

**版本**：2.1 (新增PDF版本转换功能)
**更新日期**：2025-07-03
**兼容性**：Windows系统
