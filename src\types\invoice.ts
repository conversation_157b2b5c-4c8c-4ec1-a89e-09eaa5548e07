// 发票类型枚举
export enum InvoiceType {
  WINDSURF = 'windsurf',
  WINDSURF_15 = 'windsurf_15',
  CURSOR = 'cursor'
}

// 公司信息接口
export interface CompanyInfo {
  name: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  email?: string;
  phone?: string;
  taxInfo?: string;
}

// 基础发票数据接口
export interface InvoiceData {
  type: InvoiceType;
  invoiceNumber: string;
  receiptNumber: string;
  datePaid: string;
  paymentMethod: string;
  billTo: BillToInfo;
  amount: string;
  description: string;
  dateRange: string;
  companyInfo: CompanyInfo;
}

export interface BillToInfo {
  name: string;
  company: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}
