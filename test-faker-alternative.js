// 简单的测试脚本 - 验证 Faker 备选方案
console.log('🚀 开始测试 Faker API 备选方案...\n');

// 模拟 BillToInfo 类型
const generateBillToInfoAlternative = async (email, locale = 'en_US') => {
  // 姓名数据库
  const NAME_DATABASE = {
    'en_US': {
      firstNames: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
      lastNames: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
    },
    'zh_CN': {
      firstNames: ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊'],
      lastNames: ['王', '李', '张', '刘', '陈', '杨', '赵', '黄']
    },
    'ja_JP': {
      firstNames: ['太郎', '花子', '一郎', '美咲', '翔太', '結衣'],
      lastNames: ['田中', '佐藤', '鈴木', '高橋', '渡辺', '伊藤']
    },
    'fr_FR': {
      firstNames: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
      last<PERSON>: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
    },
    'de_<PERSON>': {
      first<PERSON>: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
      last<PERSON><PERSON>: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
    }
  };

  // 地址数据库
  const ADDRESS_DATABASE = {
    'US': {
      streets: ['Main Street', 'Oak Avenue', 'Pine Road', 'Elm Street', 'Maple Drive'],
      cities: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'],
      states: ['California', 'New York', 'Texas', 'Florida', 'Illinois'],
      postcodes: () => Math.floor(Math.random() * 90000) + 10000
    },
    'CN': {
      streets: ['中山路', '人民路', '解放路', '建设路', '和平路'],
      cities: ['北京', '上海', '广州', '深圳', '杭州'],
      states: ['北京市', '上海市', '广东省', '浙江省', '江苏省'],
      postcodes: () => Math.floor(Math.random() * 900000) + 100000
    },
    'JP': {
      streets: ['中央通り', '本町通り', '駅前通り', '商店街', '大通り'],
      cities: ['東京', '大阪', '横浜', '名古屋', '札幌'],
      states: ['東京都', '大阪府', '神奈川県', '愛知県', '埼玉県'],
      postcodes: () => {
        const first = Math.floor(Math.random() * 900) + 100;
        const second = Math.floor(Math.random() * 9000) + 1000;
        return `${first}-${second}`;
      }
    },
    'FR': {
      streets: ['Rue de la Paix', 'Avenue des Champs-Élysées', 'Rue de Rivoli', 'Boulevard Saint-Germain'],
      cities: ['Paris', 'Marseille', 'Lyon', 'Toulouse', 'Nice'],
      states: ['Île-de-France', 'Auvergne-Rhône-Alpes', 'Nouvelle-Aquitaine', 'Occitanie'],
      postcodes: () => Math.floor(Math.random() * 90000) + 10000
    },
    'DE': {
      streets: ['Hauptstraße', 'Bahnhofstraße', 'Dorfstraße', 'Schulstraße', 'Gartenstraße'],
      cities: ['Berlin', 'Hamburg', 'München', 'Köln', 'Frankfurt am Main'],
      states: ['Bayern', 'Baden-Württemberg', 'Nordrhein-Westfalen', 'Hessen', 'Sachsen'],
      postcodes: () => Math.floor(Math.random() * 90000) + 10000
    }
  };

  // 国家名称映射
  const COUNTRY_NAMES = {
    'US': 'United States',
    'CN': 'China',
    'JP': 'Japan',
    'FR': 'France',
    'DE': 'Germany'
  };

  // 随机选择函数
  const randomChoice = (array) => array[Math.floor(Math.random() * array.length)];

  // 从locale推断国家代码
  const countryCode = locale.split('_')[1] || 'US';
  
  // 生成姓名
  const nameData = NAME_DATABASE[locale] || NAME_DATABASE['en_US'];
  const firstName = randomChoice(nameData.firstNames);
  const lastName = randomChoice(nameData.lastNames);
  const fullName = locale.startsWith('zh_') ? `${lastName}${firstName}` : `${firstName} ${lastName}`;
  
  // 生成地址
  const addressData = ADDRESS_DATABASE[countryCode] || ADDRESS_DATABASE['US'];
  const street = randomChoice(addressData.streets);
  const city = randomChoice(addressData.cities);
  const state = randomChoice(addressData.states);
  const postcode = typeof addressData.postcodes === 'function' ? addressData.postcodes().toString() : '12345';
  const houseNumber = Math.floor(Math.random() * 9999) + 1;
  
  return {
    name: fullName.toUpperCase(),
    address1: postcode,
    address2: `${houseNumber} ${street}`,
    city: city,
    state: state,
    country: COUNTRY_NAMES[countryCode] || 'Unknown Country',
    email: email
  };
};

// 测试不同地区
const testLocales = async () => {
  console.log('=== 测试不同地区的地址生成 ===\n');
  
  const locales = ['en_US', 'zh_CN', 'ja_JP', 'fr_FR', 'de_DE'];
  
  for (const locale of locales) {
    try {
      console.log(`--- ${locale} ---`);
      const sample = await generateBillToInfoAlternative(`test@${locale.toLowerCase()}.com`, locale);
      console.log(`姓名: ${sample.name}`);
      console.log(`地址1: ${sample.address1}`);
      console.log(`地址2: ${sample.address2}`);
      console.log(`城市: ${sample.city}`);
      console.log(`州/省: ${sample.state}`);
      console.log(`国家: ${sample.country}`);
      console.log(`邮箱: ${sample.email}`);
      console.log('');
    } catch (error) {
      console.error(`${locale} 生成失败:`, error);
    }
  }
};

// 性能测试
const testPerformance = async () => {
  console.log('=== 性能测试 ===\n');
  
  const iterations = 100;
  const locale = 'en_US';
  
  console.log(`生成 ${iterations} 个地址样本...`);
  const startTime = Date.now();
  
  const promises = [];
  for (let i = 0; i < iterations; i++) {
    promises.push(generateBillToInfoAlternative(`test${i}@example.com`, locale));
  }
  
  try {
    await Promise.all(promises);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`完成时间: ${duration}ms`);
    console.log(`平均每个: ${(duration / iterations).toFixed(2)}ms`);
    console.log(`每秒生成: ${(iterations / (duration / 1000)).toFixed(2)} 个\n`);
  } catch (error) {
    console.error('性能测试失败:', error);
  }
};

// 批量生成测试
const testBatchGeneration = async () => {
  console.log('=== 批量生成测试 ===\n');
  
  const locale = 'en_US';
  const count = 5;
  
  console.log(`批量生成 ${count} 个样本 (${locale}):`);
  
  for (let i = 0; i < count; i++) {
    const sample = await generateBillToInfoAlternative(`batch${i}@example.com`, locale);
    console.log(`${i + 1}. ${sample.name} - ${sample.city}, ${sample.state}, ${sample.country}`);
  }
  console.log('');
};

// 运行所有测试
const runAllTests = async () => {
  try {
    await testLocales();
    await testBatchGeneration();
    await testPerformance();
    
    console.log('✅ 所有测试完成！');
    console.log('\n🎉 Faker API 备选方案工作正常！');
    console.log('📝 主要特性:');
    console.log('   • 支持多种语言/地区');
    console.log('   • 本地化姓名生成');
    console.log('   • 高性能批量处理');
    console.log('   • 零网络依赖');
    console.log('   • 100% 成功率');
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
};

// 执行测试
runAllTests();
