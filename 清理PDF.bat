@echo off
chcp 65001 >nul
echo ========================================
echo 🧹 PDF元数据清理工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未安装Python
    echo 请先安装Python: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查pypdf是否安装
python -c "import pypdf" >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装pypdf库...
    pip install pypdf
    if errorlevel 1 (
        echo ❌ pypdf安装失败
        pause
        exit /b 1
    )
    echo ✅ pypdf安装成功
    echo.
)

REM 运行Python脚本
echo 🚀 启动PDF元数据清理工具...
echo.
python "清理PDF元数据.py"

pause
