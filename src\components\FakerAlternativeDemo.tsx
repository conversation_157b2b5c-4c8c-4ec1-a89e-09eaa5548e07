import React, { useState } from 'react';
import { generateBillToInfoAlternative, generateMultipleSamples } from '@/utils/fakerAlternative';
import { generateRandomInvoice } from '@/utils/invoiceGenerator';
import { BillToInfo, InvoiceData, InvoiceType } from '@/types/invoice';

const FakerAlternativeDemo: React.FC = () => {
  const [selectedLocale, setSelectedLocale] = useState('en_US');
  const [sampleCount, setSampleCount] = useState(5);
  const [samples, setSamples] = useState<BillToInfo[]>([]);
  const [invoice, setInvoice] = useState<InvoiceData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 支持的地区列表
  const locales = [
    { code: 'en_US', name: '美国英语', flag: '🇺🇸' },
    { code: 'en_GB', name: '英国英语', flag: '🇬🇧' },
    { code: 'en_CA', name: '加拿大英语', flag: '🇨🇦' },
    { code: 'en_AU', name: '澳大利亚英语', flag: '🇦🇺' },
    { code: 'zh_CN', name: '中文（简体）', flag: '🇨🇳' },
    { code: 'zh_TW', name: '中文（繁体）', flag: '🇹🇼' },
    { code: 'ja_JP', name: '日语', flag: '🇯🇵' },
    { code: 'ko_KR', name: '韩语', flag: '🇰🇷' },
    { code: 'fr_FR', name: '法语', flag: '🇫🇷' },
    { code: 'fr_CA', name: '加拿大法语', flag: '🇨🇦' },
    { code: 'de_DE', name: '德语', flag: '🇩🇪' },
    { code: 'de_AT', name: '奥地利德语', flag: '🇦🇹' },
    { code: 'es_ES', name: '西班牙语', flag: '🇪🇸' },
    { code: 'es_AR', name: '阿根廷西班牙语', flag: '🇦🇷' },
    { code: 'it_IT', name: '意大利语', flag: '🇮🇹' },
    { code: 'pt_BR', name: '巴西葡萄牙语', flag: '🇧🇷' },
    { code: 'pt_PT', name: '葡萄牙语', flag: '🇵🇹' },
    { code: 'ru_RU', name: '俄语', flag: '🇷🇺' },
    { code: 'ar_SA', name: '阿拉伯语', flag: '🇸🇦' },
    { code: 'nl_NL', name: '荷兰语', flag: '🇳🇱' },
    { code: 'sv_SE', name: '瑞典语', flag: '🇸🇪' },
    { code: 'nb_NO', name: '挪威语', flag: '🇳🇴' },
    { code: 'da_DK', name: '丹麦语', flag: '🇩🇰' }
  ];

  // 生成地址样本
  const handleGenerateSamples = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const newSamples = await generateMultipleSamples(sampleCount, selectedLocale);
      setSamples(newSamples);
    } catch (err) {
      setError(`生成样本失败: ${err instanceof Error ? err.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  // 生成完整发票
  const handleGenerateInvoice = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const testEmail = `demo@${selectedLocale.toLowerCase()}.com`;
      const newInvoice = await generateRandomInvoice(testEmail, InvoiceType.WINDSURF, selectedLocale);
      setInvoice(newInvoice);
    } catch (err) {
      setError(`生成发票失败: ${err instanceof Error ? err.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          🌍 Faker API 备选方案演示
        </h1>
        <p className="text-gray-600">
          本地化地址和姓名生成器 - 支持23种语言/地区
        </p>
      </div>

      {/* 控制面板 */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">控制面板</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择地区/语言
            </label>
            <select
              value={selectedLocale}
              onChange={(e) => setSelectedLocale(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {locales.map((locale) => (
                <option key={locale.code} value={locale.code}>
                  {locale.flag} {locale.name} ({locale.code})
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              样本数量
            </label>
            <input
              type="number"
              min="1"
              max="20"
              value={sampleCount}
              onChange={(e) => setSampleCount(parseInt(e.target.value) || 5)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex items-end">
            <div className="space-x-2">
              <button
                onClick={handleGenerateSamples}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? '生成中...' : '生成地址'}
              </button>
              <button
                onClick={handleGenerateInvoice}
                disabled={loading}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? '生成中...' : '生成发票'}
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
            <div className="flex">
              <div className="text-red-800">
                <strong>错误:</strong> {error}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 地址样本展示 */}
      {samples.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">
            📍 生成的地址样本 ({selectedLocale})
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {samples.map((sample, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="space-y-2">
                  <div className="font-semibold text-gray-900">{sample.name}</div>
                  <div className="text-sm text-gray-600">
                    <div>{sample.address1}</div>
                    <div>{sample.address2}</div>
                    <div>{sample.city}</div>
                    <div>{sample.state}</div>
                    <div className="font-medium">{sample.country}</div>
                    <div className="text-blue-600">{sample.email}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 发票展示 */}
      {invoice && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">
            🧾 生成的发票样本 ({selectedLocale})
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">发票信息</h3>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">发票号:</span> {invoice.invoiceNumber}</div>
                <div><span className="font-medium">收据号:</span> {invoice.receiptNumber}</div>
                <div><span className="font-medium">付款日期:</span> {invoice.datePaid}</div>
                <div><span className="font-medium">付款方式:</span> {invoice.paymentMethod}</div>
                <div><span className="font-medium">金额:</span> {invoice.amount}</div>
                <div><span className="font-medium">产品:</span> {invoice.description}</div>
                <div><span className="font-medium">服务期间:</span> {invoice.dateRange}</div>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">收票人信息</h3>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">姓名:</span> {invoice.billTo.name}</div>
                <div><span className="font-medium">地址1:</span> {invoice.billTo.address1}</div>
                <div><span className="font-medium">地址2:</span> {invoice.billTo.address2}</div>
                <div><span className="font-medium">城市:</span> {invoice.billTo.city}</div>
                <div><span className="font-medium">州/省:</span> {invoice.billTo.state}</div>
                <div><span className="font-medium">国家:</span> {invoice.billTo.country}</div>
                <div><span className="font-medium">邮箱:</span> {invoice.billTo.email}</div>
              </div>
              
              <h3 className="font-semibold text-gray-900 mb-3 mt-6">公司信息</h3>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">公司:</span> {invoice.companyInfo.name}</div>
                <div><span className="font-medium">地址1:</span> {invoice.companyInfo.address1}</div>
                <div><span className="font-medium">地址2:</span> {invoice.companyInfo.address2}</div>
                <div><span className="font-medium">邮箱:</span> {invoice.companyInfo.email}</div>
                {invoice.companyInfo.phone && (
                  <div><span className="font-medium">电话:</span> {invoice.companyInfo.phone}</div>
                )}
                {invoice.companyInfo.taxInfo && (
                  <div><span className="font-medium">税务信息:</span> {invoice.companyInfo.taxInfo}</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 功能说明 */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">✨ 功能特点</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">🌐 多语言支持</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 支持23种语言/地区</li>
              <li>• 本地化姓名生成</li>
              <li>• 符合当地命名习惯</li>
              <li>• 正确的地址格式</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">🚀 高性能</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 纯本地数据生成</li>
              <li>• 无网络依赖</li>
              <li>• 毫秒级响应</li>
              <li>• 支持批量生成</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">🎯 智能备选</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 三层备选机制</li>
              <li>• API失败自动切换</li>
              <li>• 保证数据质量</li>
              <li>• 零失败率</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">📍 标准化格式</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 基于Geoapify标准</li>
              <li>• 100+国家格式</li>
              <li>• 正确的邮编格式</li>
              <li>• 符合国际标准</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FakerAlternativeDemo;
