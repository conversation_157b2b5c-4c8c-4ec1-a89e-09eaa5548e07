' PDF元数据清理工具快速启动脚本
' https://learn.microsoft.com/en-us/windows/win32/shell/shell-shellexecute
' WindowStyle 窗口显示方式参数说明（官方文档）：
' 0  - 隐藏窗口启动应用程序
' 1  - 正常窗口启动应用程序（如果窗口被最小化或最大化，系统会恢复到原始大小和位置）
' 2  - 最小化窗口启动应用程序
' 3  - 最大化窗口启动应用程序
' 4  - 以最近的大小和位置显示窗口，当前活动窗口保持活动状态
' 5  - 以当前大小和位置显示窗口
' 7  - 最小化窗口启动应用程序，当前活动窗口保持活动状态
' 10 - 以应用程序指定的默认状态显示窗口

Dim CurrentTarget, ArgumentList, WorkingDirectory, Verb, WindowStyle

CurrentTarget = "start_cleaner.bat"
ArgumentList = ""
WorkingDirectory = ""
Verb = ""
WindowStyle = 1
CreateObject("Shell.Application").ShellExecute CurrentTarget, ArgumentList, WorkingDirectory, Verb, WindowStyle
