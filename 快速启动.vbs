' 发票生成器快速启动脚本
' WindowStyle 窗口显示方式参数说明：
' 0  - 隐藏窗口启动应用程序
' 1  - 正常窗口启动应用程序
' 2  - 最小化窗口启动应用程序
' 3  - 最大化窗口启动应用程序
' 7  - 最小化窗口启动应用程序，当前活动窗口保持活动状态
' 10 - 以应用程序指定的默认状态显示窗口

Dim CurrentTarget, ArgumentList, WorkingDirectory, Verb, WindowStyle

' 启动开发服务器
CurrentTarget = "cmd"
ArgumentList = "/c npm run dev"
WorkingDirectory = ""
Verb = ""
WindowStyle = 1
CreateObject("Shell.Application").ShellExecute CurrentTarget, ArgumentList, WorkingDirectory, Verb, WindowStyle

' 等待3秒让服务器启动
WScript.Sleep 3000

' 打开浏览器 - 使用 cmd 启动默认浏览器
CurrentTarget = "cmd"
ArgumentList = "/c start http://localhost:3000"
WorkingDirectory = ""
Verb = ""
WindowStyle = 0
CreateObject("Shell.Application").ShellExecute CurrentTarget, ArgumentList, WorkingDirectory, Verb, WindowStyle
