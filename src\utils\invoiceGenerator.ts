import { InvoiceData, BillToInfo, InvoiceType, CompanyInfo } from '@/types/invoice';
import { generateLocalBillToInfo } from './fakerAlternative';

// 随机生成Invoice号码 (格式: 8位十六进制数字-000+1位随机数字)
function generateInvoiceNumber(): string {
  const hexChars = '0123456789ABCDEF';
  let hexResult = '';
  for (let i = 0; i < 8; i++) {
    hexResult += hexChars.charAt(Math.floor(Math.random() * hexChars.length));
  }
  const lastDigit = Math.floor(Math.random() * 10);
  return `${hexResult}-000${lastDigit}`;
}

// 随机生成收据号码 (格式: 4位数字-4位数字)
function generateReceiptNumber(): string {
  const part1 = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  const part2 = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${part1}-${part2}`;
}

// 随机生成支付方式
function generatePaymentMethod(): string {
  const cardTypes = ['Visa', 'MasterCard', 'American Express', 'Discover'];
  const randomCardType = cardTypes[Math.floor(Math.random() * cardTypes.length)];
  const lastFourDigits = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${randomCardType} - ${lastFourDigits}`;
}

// 随机生成日期 (2025年4月17日至2025年6月16日)
function generateRandomDate(): string {
  const startDate = new Date('2025-04-17');
  const endDate = new Date('2025-06-16');
  const timeDiff = endDate.getTime() - startDate.getTime();
  const randomTime = Math.random() * timeDiff;
  const randomDate = new Date(startDate.getTime() + randomTime);
  
  return randomDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

// 根据 locale 获取国家和州/省信息
function getLocationInfoByLocale(locale: string) {
  const locationMap: { [key: string]: { country: string, states: string[] } } = {
    // 阿拉伯语系
    'ar_EG': { country: 'Egypt', states: ['Cairo', 'Alexandria', 'Giza', 'Shubra El-Kheima', 'Port Said', 'Suez', 'Luxor', 'Mansoura', 'El-Mahalla El-Kubra', 'Tanta'] },
    'ar_JO': { country: 'Jordan', states: ['Amman', 'Zarqa', 'Irbid', 'Russeifa', 'Wadi as-Sir', 'Ajloun', 'Aqaba', 'Madaba', 'Salt', 'Mafraq'] },
    'ar_SA': { country: 'Saudi Arabia', states: ['Riyadh', 'Jeddah', 'Mecca', 'Medina', 'Dammam', 'Khobar', 'Tabuk', 'Buraidah', 'Khamis Mushait', 'Hofuf'] },

    // 欧洲
    'at_AT': { country: 'Austria', states: ['Vienna', 'Graz', 'Linz', 'Salzburg', 'Innsbruck', 'Klagenfurt', 'Villach', 'Wels', 'Sankt Pölten', 'Dornbirn'] },
    'bg_BG': { country: 'Bulgaria', states: ['Sofia', 'Plovdiv', 'Varna', 'Burgas', 'Ruse', 'Stara Zagora', 'Pleven', 'Sliven', 'Dobrich', 'Shumen'] },
    'cs_CZ': { country: 'Czech Republic', states: ['Prague', 'Brno', 'Ostrava', 'Plzen', 'Liberec', 'Olomouc', 'Budweis', 'Hradec Kralove', 'Usti nad Labem', 'Pardubice'] },
    'da_DK': { country: 'Denmark', states: ['Copenhagen', 'Aarhus', 'Odense', 'Aalborg', 'Esbjerg', 'Randers', 'Kolding', 'Horsens', 'Vejle', 'Roskilde'] },
    'de_AT': { country: 'Austria', states: ['Wien', 'Graz', 'Linz', 'Salzburg', 'Innsbruck', 'Klagenfurt', 'Villach', 'Wels', 'Sankt Pölten', 'Dornbirn'] },
    'de_CH': { country: 'Switzerland', states: ['Zürich', 'Geneva', 'Basel', 'Bern', 'Lausanne', 'Winterthur', 'Lucerne', 'St. Gallen', 'Lugano', 'Biel'] },
    'de_DE': { country: 'Germany', states: ['Bayern', 'Baden-Württemberg', 'Nordrhein-Westfalen', 'Hessen', 'Sachsen', 'Niedersachsen', 'Berlin', 'Rheinland-Pfalz', 'Schleswig-Holstein', 'Brandenburg'] },
    'el_CY': { country: 'Cyprus', states: ['Nicosia', 'Limassol', 'Larnaca', 'Famagusta', 'Paphos', 'Kyrenia', 'Morphou', 'Ammochostos', 'Polis', 'Paralimni'] },
    'el_GR': { country: 'Greece', states: ['Athens', 'Thessaloniki', 'Patras', 'Piraeus', 'Larissa', 'Heraklion', 'Peristeri', 'Kallithea', 'Acharnes', 'Kalamaria'] },

    // 英语系
    'en_AU': { country: 'Australia', states: ['New South Wales', 'Victoria', 'Queensland', 'Western Australia', 'South Australia', 'Tasmania', 'Northern Territory', 'Australian Capital Territory'] },
    'en_CA': { country: 'Canada', states: ['Ontario', 'Quebec', 'British Columbia', 'Alberta', 'Manitoba', 'Saskatchewan', 'Nova Scotia', 'New Brunswick', 'Newfoundland and Labrador', 'Prince Edward Island'] },
    'en_GB': { country: 'United Kingdom', states: ['England', 'Scotland', 'Wales', 'Northern Ireland', 'London', 'Manchester', 'Birmingham', 'Liverpool', 'Bristol', 'Leeds'] },
    'en_HK': { country: 'Hong Kong', states: ['Hong Kong Island', 'Kowloon', 'New Territories', 'Central', 'Wan Chai', 'Causeway Bay', 'Tsim Sha Tsui', 'Mong Kok', 'Sha Tin', 'Tuen Mun'] },
    'en_IN': { country: 'India', states: ['Maharashtra', 'Uttar Pradesh', 'Bihar', 'West Bengal', 'Madhya Pradesh', 'Tamil Nadu', 'Rajasthan', 'Karnataka', 'Gujarat', 'Andhra Pradesh'] },
    'en_NG': { country: 'Nigeria', states: ['Lagos', 'Kano', 'Ibadan', 'Kaduna', 'Port Harcourt', 'Benin City', 'Maiduguri', 'Zaria', 'Aba', 'Jos'] },
    'en_NZ': { country: 'New Zealand', states: ['Auckland', 'Wellington', 'Christchurch', 'Hamilton', 'Tauranga', 'Napier-Hastings', 'Dunedin', 'Palmerston North', 'Nelson', 'Rotorua'] },
    'en_PH': { country: 'Philippines', states: ['Metro Manila', 'Cebu', 'Davao', 'Cagayan de Oro', 'Zamboanga', 'Antipolo', 'Pasig', 'Taguig', 'Quezon City', 'Manila'] },
    'en_SG': { country: 'Singapore', states: ['Central Region', 'East Region', 'North Region', 'North-East Region', 'West Region', 'Orchard', 'Marina Bay', 'Sentosa', 'Jurong', 'Tampines'] },
    'en_UG': { country: 'Uganda', states: ['Kampala', 'Gulu', 'Lira', 'Mbarara', 'Jinja', 'Bwizibwera', 'Mbale', 'Mukono', 'Kasese', 'Masaka'] },
    'en_US': { country: 'United States', states: ['California', 'New York', 'Texas', 'Florida', 'Illinois', 'Pennsylvania', 'Ohio', 'Georgia', 'North Carolina', 'Michigan'] },
    'en_ZA': { country: 'South Africa', states: ['Gauteng', 'Western Cape', 'KwaZulu-Natal', 'Eastern Cape', 'Limpopo', 'Mpumalanga', 'North West', 'Free State', 'Northern Cape'] },

    // 西班牙语系
    'es_AR': { country: 'Argentina', states: ['Buenos Aires', 'Córdoba', 'Santa Fe', 'Mendoza', 'Tucumán', 'Entre Ríos', 'Salta', 'Chaco', 'Corrientes', 'Misiones'] },
    'es_ES': { country: 'Spain', states: ['Madrid', 'Cataluña', 'Andalucía', 'Valencia', 'Galicia', 'Castilla y León', 'País Vasco', 'Castilla-La Mancha', 'Canarias', 'Murcia'] },
    'es_PE': { country: 'Peru', states: ['Lima', 'Arequipa', 'Trujillo', 'Chiclayo', 'Piura', 'Iquitos', 'Cusco', 'Chimbote', 'Huancayo', 'Tacna'] },
    'es_VE': { country: 'Venezuela', states: ['Caracas', 'Maracaibo', 'Valencia', 'Barquisimeto', 'Maracay', 'Ciudad Guayana', 'San Cristóbal', 'Maturín', 'Ciudad Bolívar', 'Cumaná'] },

    // 其他欧洲语言
    'et_EE': { country: 'Estonia', states: ['Tallinn', 'Tartu', 'Narva', 'Pärnu', 'Kohtla-Järve', 'Viljandi', 'Rakvere', 'Maardu', 'Sillamäe', 'Kuressaare'] },
    'fa_IR': { country: 'Iran', states: ['Tehran', 'Mashhad', 'Isfahan', 'Karaj', 'Shiraz', 'Tabriz', 'Qom', 'Ahvaz', 'Kermanshah', 'Urmia'] },
    'fi_FI': { country: 'Finland', states: ['Helsinki', 'Espoo', 'Tampere', 'Vantaa', 'Oulu', 'Turku', 'Jyväskylä', 'Lahti', 'Kuopio', 'Pori'] },
    'fr_BE': { country: 'Belgium', states: ['Brussels', 'Antwerp', 'Ghent', 'Charleroi', 'Liège', 'Bruges', 'Namur', 'Leuven', 'Mons', 'Aalst'] },
    'fr_CA': { country: 'Canada', states: ['Quebec', 'Montreal', 'Laval', 'Gatineau', 'Longueuil', 'Sherbrooke', 'Saguenay', 'Lévis', 'Trois-Rivières', 'Terrebonne'] },
    'fr_CH': { country: 'Switzerland', states: ['Genève', 'Lausanne', 'Neuchâtel', 'Fribourg', 'Sion', 'Yverdon-les-Bains', 'Montreux', 'La Chaux-de-Fonds', 'Vevey', 'Nyon'] },
    'fr_FR': { country: 'France', states: ['Île-de-France', 'Auvergne-Rhône-Alpes', 'Nouvelle-Aquitaine', 'Occitanie', 'Hauts-de-France', 'Grand Est', 'Provence-Alpes-Côte d\'Azur', 'Pays de la Loire', 'Bretagne', 'Normandie'] },

    // 中东和其他
    'he_IL': { country: 'Israel', states: ['Jerusalem', 'Tel Aviv', 'Haifa', 'Rishon LeZion', 'Petah Tikva', 'Ashdod', 'Netanya', 'Beer Sheva', 'Holon', 'Bnei Brak'] },
    'hr_HR': { country: 'Croatia', states: ['Zagreb', 'Split', 'Rijeka', 'Osijek', 'Zadar', 'Slavonski Brod', 'Pula', 'Sesvete', 'Karlovac', 'Varaždin'] },
    'hu_HU': { country: 'Hungary', states: ['Budapest', 'Debrecen', 'Szeged', 'Miskolc', 'Pécs', 'Győr', 'Nyíregyháza', 'Kecskemét', 'Székesfehérvár', 'Szombathely'] },
    'hy_AM': { country: 'Armenia', states: ['Yerevan', 'Gyumri', 'Vanadzor', 'Vagharshapat', 'Hrazdan', 'Abovyan', 'Kapan', 'Armavir', 'Goris', 'Artashat'] },

    // 亚洲
    'id_ID': { country: 'Indonesia', states: ['Jakarta', 'Surabaya', 'Bandung', 'Bekasi', 'Medan', 'Tangerang', 'Depok', 'Semarang', 'Palembang', 'Makassar'] },
    'is_IS': { country: 'Iceland', states: ['Reykjavík', 'Kópavogur', 'Hafnarfjörður', 'Akureyri', 'Reykjanesbær', 'Garðabær', 'Mosfellsbær', 'Árborg', 'Akranes', 'Fjarðabyggð'] },
    'it_CH': { country: 'Switzerland', states: ['Lugano', 'Bellinzona', 'Locarno', 'Mendrisio', 'Chiasso', 'Biasca', 'Ascona', 'Massagno', 'Paradiso', 'Manno'] },
    'it_IT': { country: 'Italy', states: ['Lombardia', 'Lazio', 'Campania', 'Sicilia', 'Veneto', 'Emilia-Romagna', 'Piemonte', 'Puglia', 'Toscana', 'Calabria'] },
    'ja_JP': { country: 'Japan', states: ['東京都', '大阪府', '神奈川県', '愛知県', '埼玉県', '千葉県', '兵庫県', '北海道', '福岡県', '静岡県'] },
    'ka_GE': { country: 'Georgia', states: ['Tbilisi', 'Kutaisi', 'Batumi', 'Rustavi', 'Zugdidi', 'Gori', 'Poti', 'Kobuleti', 'Khashuri', 'Samtredia'] },
    'kk_KZ': { country: 'Kazakhstan', states: ['Almaty', 'Nur-Sultan', 'Shymkent', 'Aktobe', 'Taraz', 'Pavlodar', 'Ust-Kamenogorsk', 'Karaganda', 'Semey', 'Atyrau'] },
    'ko_KR': { country: 'South Korea', states: ['서울특별시', '부산광역시', '대구광역시', '인천광역시', '광주광역시', '대전광역시', '울산광역시', '경기도', '강원도', '충청북도'] },

    // 波罗的海和北欧
    'lt_LT': { country: 'Lithuania', states: ['Vilnius', 'Kaunas', 'Klaipėda', 'Šiauliai', 'Panevėžys', 'Alytus', 'Marijampolė', 'Mažeikiai', 'Jonava', 'Utena'] },
    'lv_LV': { country: 'Latvia', states: ['Riga', 'Daugavpils', 'Liepāja', 'Jelgava', 'Jūrmala', 'Ventspils', 'Rēzekne', 'Valmiera', 'Jēkabpils', 'Ogre'] },
    'me_ME': { country: 'Montenegro', states: ['Podgorica', 'Nikšić', 'Pljevlja', 'Bijelo Polje', 'Cetinje', 'Bar', 'Herceg Novi', 'Berane', 'Budva', 'Ulcinj'] },
    'mn_MN': { country: 'Mongolia', states: ['Ulaanbaatar', 'Erdenet', 'Darkhan', 'Choibalsan', 'Murun', 'Bayankhongor', 'Olgii', 'Hovd', 'Arvayheer', 'Mandalgovi'] },
    'ms_MY': { country: 'Malaysia', states: ['Kuala Lumpur', 'George Town', 'Ipoh', 'Shah Alam', 'Petaling Jaya', 'Johor Bahru', 'Seremban', 'Kuala Terengganu', 'Kota Kinabalu', 'Klang'] },
    'nb_NO': { country: 'Norway', states: ['Oslo', 'Bergen', 'Stavanger', 'Trondheim', 'Drammen', 'Fredrikstad', 'Kristiansand', 'Sandnes', 'Tromsø', 'Sarpsborg'] },
    'ne_NP': { country: 'Nepal', states: ['Kathmandu', 'Pokhara', 'Lalitpur', 'Bharatpur', 'Biratnagar', 'Birgunj', 'Dharan', 'Butwal', 'Hetauda', 'Nepalgunj'] },
    'nl_BE': { country: 'Belgium', states: ['Antwerpen', 'Gent', 'Brugge', 'Leuven', 'Aalst', 'Mechelen', 'Kortrijk', 'Hasselt', 'Sint-Niklaas', 'Oostende'] },
    'nl_NL': { country: 'Netherlands', states: ['Noord-Holland', 'Zuid-Holland', 'Noord-Brabant', 'Gelderland', 'Utrecht', 'Overijssel', 'Limburg', 'Friesland', 'Groningen', 'Drenthe'] },
    'pl_PL': { country: 'Poland', states: ['Mazowieckie', 'Śląskie', 'Wielkopolskie', 'Małopolskie', 'Dolnośląskie', 'Łódzkie', 'Kujawsko-Pomorskie', 'Pomorskie', 'Lubelskie', 'Podkarpackie'] },
    'pt_BR': { country: 'Brazil', states: ['São Paulo', 'Rio de Janeiro', 'Minas Gerais', 'Bahia', 'Paraná', 'Rio Grande do Sul', 'Pernambuco', 'Ceará', 'Pará', 'Santa Catarina'] },
    'pt_PT': { country: 'Portugal', states: ['Lisboa', 'Porto', 'Braga', 'Setúbal', 'Coimbra', 'Funchal', 'Almada', 'Agualva-Cacém', 'Queluz', 'Amadora'] },
    'ro_MD': { country: 'Moldova', states: ['Chișinău', 'Tiraspol', 'Bălți', 'Bender', 'Rîbnița', 'Cahul', 'Ungheni', 'Soroca', 'Orhei', 'Comrat'] },
    'ro_RO': { country: 'Romania', states: ['București', 'Cluj-Napoca', 'Timișoara', 'Iași', 'Constanța', 'Craiova', 'Brașov', 'Galați', 'Ploiești', 'Oradea'] },
    'ru_RU': { country: 'Russia', states: ['Москва', 'Санкт-Петербург', 'Новосибирск', 'Екатеринбург', 'Нижний Новгород', 'Казань', 'Челябинск', 'Омск', 'Самара', 'Ростов-на-Дону'] },
    'sk_SK': { country: 'Slovakia', states: ['Bratislava', 'Košice', 'Prešov', 'Žilina', 'Banská Bystrica', 'Nitra', 'Trnava', 'Martin', 'Trenčín', 'Poprad'] },
    'sl_SI': { country: 'Slovenia', states: ['Ljubljana', 'Maribor', 'Celje', 'Kranj', 'Velenje', 'Koper', 'Novo Mesto', 'Ptuj', 'Trbovlje', 'Kamnik'] },
    'sr_Cyrl_RS': { country: 'Serbia', states: ['Београд', 'Нови Сад', 'Ниш', 'Крагујевац', 'Суботица', 'Зрењанин', 'Панчево', 'Чачак', 'Нови Пазар', 'Кикинда'] },
    'sr_Latn_RS': { country: 'Serbia', states: ['Beograd', 'Novi Sad', 'Niš', 'Kragujevac', 'Subotica', 'Zrenjanin', 'Pančevo', 'Čačak', 'Novi Pazar', 'Kikinda'] },
    'sr_RS': { country: 'Serbia', states: ['Belgrade', 'Novi Sad', 'Niš', 'Kragujevac', 'Subotica', 'Zrenjanin', 'Pančevo', 'Čačak', 'Novi Pazar', 'Kikinda'] },
    'sv_SE': { country: 'Sweden', states: ['Stockholm', 'Göteborg', 'Malmö', 'Uppsala', 'Västerås', 'Örebro', 'Linköping', 'Helsingborg', 'Jönköping', 'Norrköping'] },
    'th_TH': { country: 'Thailand', states: ['Bangkok', 'Samut Prakan', 'Mueang Nonthaburi', 'Udon Thani', 'Chon Buri', 'Nakhon Ratchasima', 'Chiang Mai', 'Hat Yai', 'Ubon Ratchathani', 'Khon Kaen'] },
    'tr_TR': { country: 'Turkey', states: ['Istanbul', 'Ankara', 'Izmir', 'Bursa', 'Adana', 'Gaziantep', 'Konya', 'Antalya', 'Kayseri', 'Mersin'] },
    'uk_UA': { country: 'Ukraine', states: ['Київ', 'Харків', 'Одеса', 'Дніпро', 'Донецьк', 'Запоріжжя', 'Львів', 'Кривий Ріг', 'Миколаїв', 'Маріуполь'] },
    'vi_VN': { country: 'Vietnam', states: ['Ho Chi Minh City', 'Hanoi', 'Haiphong', 'Da Nang', 'Bien Hoa', 'Hue', 'Nha Trang', 'Can Tho', 'Rach Gia', 'Qui Nhon'] },
    'zh_CN': { country: 'China', states: ['北京市', '上海市', '广东省', '浙江省', '江苏省', '山东省', '河南省', '四川省', '湖北省', '福建省'] },
    'zh_TW': { country: 'Taiwan', states: ['台北市', '新北市', '桃園市', '台中市', '台南市', '高雄市', '基隆市', '新竹市', '嘉義市', '新竹县'] }
  };

  return locationMap[locale] || locationMap['en_US'];
}

// 使用两层备选方案生成收票人信息：API → 本地生成器
async function generateBillToInfoFromAPI(email: string, locale: string = 'en_US'): Promise<BillToInfo> {
  // 第一层：尝试使用 Faker API
  try {
    // 使用 AbortController 实现超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

    const response = await fetch(`https://fakerapi.it/api/v2/persons?_quantity=1&_locale=${locale}`, {
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    const data = await response.json();

    if (data.status === 'OK' && data.data && data.data.length > 0) {
      const person = data.data[0];
      const locationInfo = getLocationInfoByLocale(locale);

      // 随机选择一个州/省
      const randomState = locationInfo.states[Math.floor(Math.random() * locationInfo.states.length)];

      // 根据不同国家格式化地址
      if (locale === 'zh_CN') {
        // 中文地址格式
        return {
          name: `${person.firstname}${person.lastname}`,
          company: `${person.lastname}公司`,
          email: email,
          phone: `+86 138-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
          address: person.address.street,
          city: person.address.city,
          state: randomState,
          postalCode: person.address.zipcode,
          country: locationInfo.country
        };
      } else if (locale === 'ja_JP') {
        // 日文地址格式
        return {
          name: `${person.firstname} ${person.lastname}`,
          company: `${person.lastname}株式会社`,
          email: email,
          phone: `+81 90-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
          address: person.address.street,
          city: person.address.city,
          state: randomState,
          postalCode: person.address.zipcode,
          country: locationInfo.country
        };
      } else {
        // 西方国家地址格式
        return {
          name: `${person.firstname} ${person.lastname}`.toUpperCase(),
          company: `${person.lastname} Corp`,
          email: email,
          phone: `******-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
          address: person.address.street,
          city: person.address.city,
          state: randomState,
          postalCode: person.address.zipcode,
          country: locationInfo.country
        };
      }
    }
  } catch (error) {
    console.warn('Faker API failed, using local generator:', error);
  }

  // 第二层：使用本地高质量数据生成器
  console.log('🏠 使用本地数据生成器');
  return generateLocalBillToInfo(locale);
}



// 生成日期范围 (基于支付日期)
function generateDateRange(datePaid: string): string {
  const paidDate = new Date(datePaid);
  const startDate = new Date(paidDate);
  const endDate = new Date(paidDate);
  endDate.setMonth(endDate.getMonth() + 1);
  
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };
  
  return `${formatDate(startDate)} - ${formatDate(endDate)}, ${paidDate.getFullYear()}`;
}

// 生成公司信息
function generateCompanyInfo(type: InvoiceType): CompanyInfo {
  if (type === InvoiceType.WINDSURF || type === InvoiceType.WINDSURF_15) {
    return {
      name: 'Windsurf',
      address: '900 Villa Street',
      city: 'Mountain View',
      state: 'California',
      postalCode: '94041',
      country: 'United States',
      email: '<EMAIL>',
      taxInfo: 'EU OSS VAT EU372077851'
    };
  } else {
    return {
      name: 'Cursor',
      address: '801 West End Avenue',
      city: 'New York',
      state: 'New York',
      postalCode: '10025',
      country: 'United States',
      phone: '******-425-9504',
      email: '<EMAIL>',
      taxInfo: 'Anysphere, Inc.\nUS EIN 87-4436547'
    };
  }
}

// 根据发票类型生成产品信息
function generateProductInfo(type: InvoiceType): { amount: string; description: string } {
  if (type === InvoiceType.WINDSURF) {
    return {
      amount: '$6.90',
      description: 'Windsurf Pro'
    };
  } else if (type === InvoiceType.WINDSURF_15) {
    return {
      amount: '$15.00',
      description: 'Windsurf Pro'
    };
  } else {
    return {
      amount: '$20.00',
      description: 'Cursor Pro'
    };
  }
}

// 主要的Invoice生成函数
export async function generateRandomInvoice(email: string, type: InvoiceType = InvoiceType.WINDSURF, locale: string = 'en_US'): Promise<InvoiceData> {
  const invoiceNumber = generateInvoiceNumber();
  const receiptNumber = generateReceiptNumber();
  const datePaid = generateRandomDate();
  const paymentMethod = generatePaymentMethod();
  const billTo = await generateBillToInfoFromAPI(email, locale);
  const productInfo = generateProductInfo(type);
  const companyInfo = generateCompanyInfo(type);
  const dateRange = generateDateRange(datePaid);

  return {
    type,
    invoiceNumber,
    receiptNumber,
    datePaid,
    paymentMethod,
    billTo,
    amount: productInfo.amount,
    description: productInfo.description,
    dateRange,
    companyInfo
  };
}

// 强制使用本地数据生成器的Invoice生成函数
export async function generateRandomInvoiceLocal(email: string, type: InvoiceType = InvoiceType.WINDSURF, locale: string = 'en_US'): Promise<InvoiceData> {
  const invoiceNumber = generateInvoiceNumber();
  const receiptNumber = generateReceiptNumber();
  const datePaid = generateRandomDate();
  const paymentMethod = generatePaymentMethod();

  // 直接使用本地数据生成器，跳过API调用
  console.log('🏠 强制使用本地数据生成器');
  const billTo = generateLocalBillToInfo(locale);

  // 使用用户输入的邮箱替换随机生成的邮箱
  billTo.email = email;

  const productInfo = generateProductInfo(type);
  const companyInfo = generateCompanyInfo(type);
  const dateRange = generateDateRange(datePaid);

  return {
    type,
    invoiceNumber,
    receiptNumber,
    datePaid,
    paymentMethod,
    billTo,
    amount: productInfo.amount,
    description: productInfo.description,
    dateRange,
    companyInfo
  };
}
