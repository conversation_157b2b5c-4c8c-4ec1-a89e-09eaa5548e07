#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF元数据清理工具
用于移除PDF文件中的元数据信息，包括Title、Creator、Producer、CreationDate、ModDate等

使用方法：
1. 安装依赖：pip install pypdf
2. 运行脚本：python 清理PDF元数据.py
3. 选择要清理的PDF文件
4. 脚本会生成一个清理后的新文件

作者：AI Assistant
版本：2.0 (使用最新的pypdf库)
"""

import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox
from pypdf import PdfReader, PdfWriter
import datetime

def clean_pdf_metadata(input_path, output_path=None, pdf_version=None):
    """
    清理PDF文件的元数据并可选择转换PDF版本

    Args:
        input_path (str): 输入PDF文件路径
        output_path (str): 输出PDF文件路径，如果为None则自动生成
        pdf_version (str): 目标PDF版本 (如 "1.4", "1.5", "1.6", "1.7", "2.0")

    Returns:
        tuple: (成功状态, 结果信息)
    """
    try:
        # 读取原PDF文件
        reader = PdfReader(input_path)
        writer = PdfWriter()
        
        # 复制所有页面
        for page in reader.pages:
            writer.add_page(page)

        # 设置PDF版本（如果指定）
        if pdf_version:
            try:
                # 验证版本格式
                valid_versions = ["1.3", "1.4", "1.5", "1.6", "1.7", "2.0"]
                if pdf_version not in valid_versions:
                    return False, f"不支持的PDF版本: {pdf_version}。支持的版本: {', '.join(valid_versions)}"

                # 设置PDF版本
                writer.pdf_version = pdf_version
                print(f"  📄 设置PDF版本为: {pdf_version}")
            except Exception as e:
                print(f"  ⚠️  PDF版本设置失败: {e}")
                # 继续处理，不因版本设置失败而中断
        
        # 使用pypdf的最新方法彻底清理元数据
        # 方法1：设置为空字符串
        writer.add_metadata({
            '/Title': '',
            '/Author': '',
            '/Subject': '',
            '/Creator': '',
            '/Producer': '',
            '/Keywords': '',
            '/CreationDate': '',
            '/ModDate': '',
            '/Trapped': '',
            '/PTEX.Fullbanner': ''
        })

        # 方法2：如果支持，完全移除元数据字典
        try:
            # 尝试清空文档信息字典
            if hasattr(writer, '_info') and writer._info:
                writer._info.clear()
        except:
            pass
        
        # 生成输出文件名
        if output_path is None:
            base_name = os.path.splitext(input_path)[0]
            output_path = f"{base_name}_cleaned.pdf"
        
        # 写入新PDF文件
        with open(output_path, 'wb') as output_file:
            writer.write(output_file)
        
        return True, output_path
        
    except Exception as e:
        return False, str(e)

def select_file_and_clean():
    """
    通过GUI选择文件并清理元数据
    """
    # 创建隐藏的根窗口
    root = tk.Tk()
    root.withdraw()

    # 选择PDF文件
    file_path = filedialog.askopenfilename(
        title="选择要清理元数据的PDF文件",
        filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
    )

    if not file_path:
        print("未选择文件，程序退出。")
        return

    print(f"选择的文件: {file_path}")

    # 询问是否需要转换PDF版本
    pdf_version = None
    print("\n是否需要转换PDF版本？")
    print("支持的版本: 1.3, 1.4, 1.5, 1.6, 1.7, 2.0")
    version_choice = input("输入目标版本(直接回车跳过): ").strip()

    if version_choice:
        valid_versions = ["1.3", "1.4", "1.5", "1.6", "1.7", "2.0"]
        if version_choice in valid_versions:
            pdf_version = version_choice
            print(f"✅ 将转换为PDF版本: {pdf_version}")
        else:
            print(f"⚠️  无效版本，跳过版本转换")

    # 清理元数据
    success, result = clean_pdf_metadata(file_path, pdf_version=pdf_version)

    if success:
        print(f"✅ 元数据清理成功！")
        print(f"📁 清理后的文件: {result}")

        # 显示成功消息
        version_info = f"\nPDF版本: {pdf_version}" if pdf_version else ""
        messagebox.showinfo(
            "清理完成",
            f"PDF元数据清理成功！{version_info}\n\n原文件: {os.path.basename(file_path)}\n清理后: {os.path.basename(result)}"
        )
    else:
        print(f"❌ 清理失败: {result}")
        messagebox.showerror("清理失败", f"PDF元数据清理失败:\n{result}")

def batch_clean_folder():
    """
    批量清理文件夹中的所有PDF文件
    """
    root = tk.Tk()
    root.withdraw()

    folder_path = filedialog.askdirectory(title="选择包含PDF文件的文件夹")

    if not folder_path:
        print("未选择文件夹，程序退出。")
        return

    pdf_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.pdf')]

    if not pdf_files:
        messagebox.showinfo("提示", "所选文件夹中没有找到PDF文件。")
        return

    # 询问是否需要转换PDF版本
    pdf_version = None
    print(f"\n找到 {len(pdf_files)} 个PDF文件")
    print("是否需要批量转换PDF版本？")
    print("支持的版本: 1.3, 1.4, 1.5, 1.6, 1.7, 2.0")
    version_choice = input("输入目标版本(直接回车跳过): ").strip()

    if version_choice:
        valid_versions = ["1.3", "1.4", "1.5", "1.6", "1.7", "2.0"]
        if version_choice in valid_versions:
            pdf_version = version_choice
            print(f"✅ 将批量转换为PDF版本: {pdf_version}")
        else:
            print(f"⚠️  无效版本，跳过版本转换")

    success_count = 0
    failed_files = []

    print(f"\n开始批量清理...")

    for pdf_file in pdf_files:
        input_path = os.path.join(folder_path, pdf_file)
        print(f"正在处理: {pdf_file}")

        success, result = clean_pdf_metadata(input_path, pdf_version=pdf_version)

        if success:
            success_count += 1
            print(f"  ✅ 成功")
        else:
            failed_files.append(f"{pdf_file}: {result}")
            print(f"  ❌ 失败: {result}")

    # 显示批量处理结果
    version_info = f"\nPDF版本转换: {pdf_version}" if pdf_version else ""
    message = f"批量处理完成！{version_info}\n\n成功处理: {success_count} 个文件"
    if failed_files:
        message += f"\n失败: {len(failed_files)} 个文件\n\n失败详情:\n" + "\n".join(failed_files[:5])
        if len(failed_files) > 5:
            message += f"\n... 还有 {len(failed_files) - 5} 个失败文件"

    messagebox.showinfo("批量处理完成", message)

def main():
    """
    主函数
    """
    print("=" * 50)
    print("🧹 PDF元数据清理工具")
    print("=" * 50)
    print()
    print("此工具可以清理PDF文件中的以下元数据:")
    print("• Title (标题)")
    print("• Creator (创建者)")
    print("• Producer (生成器)")
    print("• CreationDate (创建日期)")
    print("• ModDate (修改日期)")
    print("• Author (作者)")
    print("• Subject (主题)")
    print("• Keywords (关键词)")
    print()
    print("🔄 额外功能:")
    print("• PDF版本转换 (支持 1.3, 1.4, 1.5, 1.6, 1.7, 2.0)")
    print("• 批量处理文件夹")
    print()
    
    # 检查是否安装了pypdf
    try:
        import pypdf
    except ImportError:
        print("❌ 错误: 未安装 pypdf 库")
        print("请运行以下命令安装:")
        print("pip install pypdf")
        input("按回车键退出...")
        return
    
    while True:
        print("请选择操作:")
        print("1. 清理单个PDF文件 (支持版本转换)")
        print("2. 批量清理文件夹中的所有PDF (支持版本转换)")
        print("3. 退出")
        print()
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == '1':
            select_file_and_clean()
        elif choice == '2':
            batch_clean_folder()
        elif choice == '3':
            print("程序退出。")
            break
        else:
            print("❌ 无效选择，请重新输入。")
        
        print("\n" + "=" * 50 + "\n")

if __name__ == "__main__":
    main()
