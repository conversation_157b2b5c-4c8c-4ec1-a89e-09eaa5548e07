// 测试 Faker API 备选方案
import { generateBillToInfoAlternative, generateMultipleSamples } from './fakerAlternative';
import { generateRandomInvoice } from './invoiceGenerator';
import { InvoiceType } from '@/types/invoice';

// 测试不同locale的地址生成
export async function testDifferentLocales() {
  console.log('=== 测试不同地区的地址生成 ===\n');
  
  const locales = [
    'en_US', 'en_GB', 'en_CA', 'en_AU',
    'zh_CN', 'zh_TW', 'ja_JP', 'ko_KR',
    'fr_FR', 'fr_CA', 'de_DE', 'de_AT',
    'es_ES', 'es_AR', 'it_IT', 'pt_BR',
    'ru_RU', 'ar_SA', 'nl_NL', 'sv_SE'
  ];
  
  for (const locale of locales) {
    try {
      console.log(`--- ${locale} ---`);
      const sample = await generateBillToInfoAlternative(`test@${locale.toLowerCase()}.com`, locale);
      console.log(`姓名: ${sample.name}`);
      console.log(`地址1: ${sample.address1}`);
      console.log(`地址2: ${sample.address2}`);
      console.log(`城市: ${sample.city}`);
      console.log(`州/省: ${sample.state}`);
      console.log(`国家: ${sample.country}`);
      console.log(`邮箱: ${sample.email}`);
      console.log('');
    } catch (error) {
      console.error(`${locale} 生成失败:`, error);
    }
  }
}

// 测试批量生成
export async function testBatchGeneration() {
  console.log('=== 测试批量生成 ===\n');
  
  const testLocales = ['en_US', 'zh_CN', 'ja_JP', 'fr_FR', 'de_DE'];
  
  for (const locale of testLocales) {
    console.log(`--- ${locale} 批量生成 (5个样本) ---`);
    try {
      const samples = await generateMultipleSamples(5, locale);
      samples.forEach((sample, index) => {
        console.log(`${index + 1}. ${sample.name} - ${sample.city}, ${sample.state}, ${sample.country}`);
      });
      console.log('');
    } catch (error) {
      console.error(`${locale} 批量生成失败:`, error);
    }
  }
}

// 测试完整的发票生成
export async function testFullInvoiceGeneration() {
  console.log('=== 测试完整发票生成 ===\n');
  
  const testCases = [
    { email: '<EMAIL>', locale: 'en_US', type: InvoiceType.WINDSURF },
    { email: '<EMAIL>', locale: 'zh_CN', type: InvoiceType.WINDSURF_15 },
    { email: '<EMAIL>', locale: 'ja_JP', type: InvoiceType.CURSOR },
    { email: '<EMAIL>', locale: 'fr_FR', type: InvoiceType.WINDSURF },
    { email: '<EMAIL>', locale: 'de_DE', type: InvoiceType.CURSOR }
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`--- ${testCase.locale} (${testCase.type}) ---`);
      const invoice = await generateRandomInvoice(testCase.email, testCase.type, testCase.locale);
      
      console.log(`发票号: ${invoice.invoiceNumber}`);
      console.log(`收据号: ${invoice.receiptNumber}`);
      console.log(`付款日期: ${invoice.datePaid}`);
      console.log(`付款方式: ${invoice.paymentMethod}`);
      console.log(`金额: ${invoice.amount}`);
      console.log(`产品: ${invoice.description}`);
      console.log(`收票人: ${invoice.billTo.name}`);
      console.log(`地址: ${invoice.billTo.address2}, ${invoice.billTo.city}`);
      console.log(`公司: ${invoice.companyInfo.name}`);
      console.log('');
    } catch (error) {
      console.error(`${testCase.locale} 发票生成失败:`, error);
    }
  }
}

// 性能测试
export async function testPerformance() {
  console.log('=== 性能测试 ===\n');
  
  const iterations = 100;
  const locale = 'en_US';
  
  console.log(`生成 ${iterations} 个地址样本...`);
  const startTime = Date.now();
  
  const promises = [];
  for (let i = 0; i < iterations; i++) {
    promises.push(generateBillToInfoAlternative(`test${i}@example.com`, locale));
  }
  
  try {
    await Promise.all(promises);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`完成时间: ${duration}ms`);
    console.log(`平均每个: ${(duration / iterations).toFixed(2)}ms`);
    console.log(`每秒生成: ${(iterations / (duration / 1000)).toFixed(2)} 个`);
  } catch (error) {
    console.error('性能测试失败:', error);
  }
}

// 地址格式验证测试
export async function testAddressFormats() {
  console.log('=== 地址格式验证测试 ===\n');
  
  const formatTests = [
    { locale: 'en_US', expectedPattern: /^\d{5}$/ }, // 美国邮编
    { locale: 'en_CA', expectedPattern: /^[A-Z]\d[A-Z] \d[A-Z]\d$/ }, // 加拿大邮编
    { locale: 'en_GB', expectedPattern: /^[A-Z]{2}\d \d[A-Z]{2}$/ }, // 英国邮编
    { locale: 'de_DE', expectedPattern: /^\d{5}$/ }, // 德国邮编
    { locale: 'fr_FR', expectedPattern: /^\d{5}$/ }, // 法国邮编
    { locale: 'ja_JP', expectedPattern: /^\d{3}-\d{4}$/ }, // 日本邮编
    { locale: 'zh_CN', expectedPattern: /^\d{6}$/ } // 中国邮编
  ];
  
  for (const test of formatTests) {
    try {
      console.log(`--- ${test.locale} 邮编格式验证 ---`);
      const samples = await generateMultipleSamples(10, test.locale);
      
      let validCount = 0;
      samples.forEach((sample, index) => {
        const isValid = test.expectedPattern.test(sample.address1);
        console.log(`${index + 1}. ${sample.address1} - ${isValid ? '✓' : '✗'}`);
        if (isValid) validCount++;
      });
      
      console.log(`验证通过率: ${(validCount / samples.length * 100).toFixed(1)}%\n`);
    } catch (error) {
      console.error(`${test.locale} 格式验证失败:`, error);
    }
  }
}

// 运行所有测试
export async function runAllTests() {
  console.log('🚀 开始运行 Faker API 备选方案测试\n');
  
  try {
    await testDifferentLocales();
    await testBatchGeneration();
    await testFullInvoiceGeneration();
    await testPerformance();
    await testAddressFormats();
    
    console.log('✅ 所有测试完成！');
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 如果直接运行此文件，执行所有测试
if (typeof window === 'undefined') {
  // Node.js 环境
  runAllTests();
}
