// 测试本地Invoice生成功能
console.log('🧾 测试本地Invoice生成功能...\n');

// 模拟 InvoiceType 枚举
const InvoiceType = {
  WINDSURF: 'WINDSURF',
  WINDSURF_15: 'WINDSURF_15',
  CURSOR: 'CURSOR'
};

// 模拟本地数据生成器
const generateBillToInfoAlternative = async (email, locale = 'en_US') => {
  const NAME_DATABASE = {
    'en_US': {
      firstNames: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
      lastNames: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
    },
    'zh_CN': {
      firstNames: ['伟', '芳', '娜', '敏', '静', '丽'],
      lastNames: ['王', '李', '张', '刘', '陈', '杨']
    },
    'ja_<PERSON>': {
      firstNames: ['太郎', '花子', '一郎', '美咲'],
      lastNames: ['田中', '佐藤', '鈴木', '高橋']
    }
  };

  const ADDRESS_DATABASE = {
    'US': {
      streets: ['Main Street', 'Oak Avenue', 'Pine Road'],
      cities: ['New York', 'Los Angeles', 'Chicago'],
      states: ['California', 'New York', 'Texas'],
      postcodes: () => Math.floor(Math.random() * 90000) + 10000
    },
    'CN': {
      streets: ['中山路', '人民路', '解放路'],
      cities: ['北京', '上海', '广州'],
      states: ['北京市', '上海市', '广东省'],
      postcodes: () => Math.floor(Math.random() * 900000) + 100000
    },
    'JP': {
      streets: ['中央通り', '本町通り', '駅前通り'],
      cities: ['東京', '大阪', '横浜'],
      states: ['東京都', '大阪府', '神奈川県'],
      postcodes: () => {
        const first = Math.floor(Math.random() * 900) + 100;
        const second = Math.floor(Math.random() * 9000) + 1000;
        return `${first}-${second}`;
      }
    }
  };

  const COUNTRY_NAMES = {
    'US': 'United States',
    'CN': 'China',
    'JP': 'Japan'
  };

  const randomChoice = (array) => array[Math.floor(Math.random() * array.length)];
  const countryCode = locale.split('_')[1] || 'US';
  
  const nameData = NAME_DATABASE[locale] || NAME_DATABASE['en_US'];
  const firstName = randomChoice(nameData.firstNames);
  const lastName = randomChoice(nameData.lastNames);
  const fullName = locale.startsWith('zh_') ? `${lastName}${firstName}` : `${firstName} ${lastName}`;
  
  const addressData = ADDRESS_DATABASE[countryCode] || ADDRESS_DATABASE['US'];
  const street = randomChoice(addressData.streets);
  const city = randomChoice(addressData.cities);
  const state = randomChoice(addressData.states);
  const postcode = addressData.postcodes().toString();
  const houseNumber = Math.floor(Math.random() * 9999) + 1;
  
  return {
    name: fullName.toUpperCase(),
    address1: postcode,
    address2: `${houseNumber} ${street}`,
    city: city,
    state: state,
    country: COUNTRY_NAMES[countryCode] || 'Unknown Country',
    email: email
  };
};

// 模拟Invoice生成器的其他函数
const generateInvoiceNumber = () => `INV-${Date.now().toString().slice(-8)}`;
const generateReceiptNumber = () => `RCP-${Math.random().toString(36).substr(2, 8).toUpperCase()}`;
const generateRandomDate = () => {
  const date = new Date();
  date.setDate(date.getDate() - Math.floor(Math.random() * 30));
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
};
const generatePaymentMethod = () => {
  const methods = ['Credit Card', 'PayPal', 'Bank Transfer', 'Stripe'];
  return methods[Math.floor(Math.random() * methods.length)];
};

const generateProductInfo = (type) => {
  const products = {
    [InvoiceType.WINDSURF]: { amount: '$6.90', description: 'Windsurf Pro Subscription' },
    [InvoiceType.WINDSURF_15]: { amount: '$15.00', description: 'Windsurf Pro Subscription' },
    [InvoiceType.CURSOR]: { amount: '$20.00', description: 'Cursor Pro Subscription' }
  };
  return products[type] || products[InvoiceType.WINDSURF];
};

const generateCompanyInfo = (type) => {
  const companies = {
    [InvoiceType.WINDSURF]: {
      name: 'Codeium Inc.',
      address1: '1234 Tech Street',
      address2: 'Suite 100',
      email: '<EMAIL>'
    },
    [InvoiceType.CURSOR]: {
      name: 'Anysphere Inc.',
      address1: '5678 Innovation Ave',
      address2: 'Floor 5',
      email: '<EMAIL>'
    }
  };
  return companies[type] || companies[InvoiceType.WINDSURF];
};

const generateDateRange = (datePaid) => {
  const startDate = new Date(datePaid);
  const endDate = new Date(startDate);
  endDate.setMonth(endDate.getMonth() + 1);
  
  return `${startDate.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric', 
    year: 'numeric' 
  })} - ${endDate.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric', 
    year: 'numeric' 
  })}`;
};

// 本地Invoice生成函数
const generateRandomInvoiceLocal = async (email, type = InvoiceType.WINDSURF, locale = 'en_US') => {
  console.log(`🏠 使用本地数据生成器生成Invoice (${locale})`);
  
  const invoiceNumber = generateInvoiceNumber();
  const receiptNumber = generateReceiptNumber();
  const datePaid = generateRandomDate();
  const paymentMethod = generatePaymentMethod();
  const billTo = await generateBillToInfoAlternative(email, locale);
  const productInfo = generateProductInfo(type);
  const companyInfo = generateCompanyInfo(type);
  const dateRange = generateDateRange(datePaid);

  return {
    type,
    invoiceNumber,
    receiptNumber,
    datePaid,
    paymentMethod,
    billTo,
    amount: productInfo.amount,
    description: productInfo.description,
    dateRange,
    companyInfo
  };
};

// 测试不同类型的Invoice生成
const testInvoiceGeneration = async () => {
  console.log('=== 测试不同类型的Invoice生成 ===\n');
  
  const testCases = [
    { email: '<EMAIL>', type: InvoiceType.WINDSURF, locale: 'en_US' },
    { email: '<EMAIL>', type: InvoiceType.WINDSURF_15, locale: 'zh_CN' },
    { email: '<EMAIL>', type: InvoiceType.CURSOR, locale: 'ja_JP' }
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`--- ${testCase.type} (${testCase.locale}) ---`);
      const invoice = await generateRandomInvoiceLocal(testCase.email, testCase.type, testCase.locale);
      
      console.log(`Invoice号: ${invoice.invoiceNumber}`);
      console.log(`收据号: ${invoice.receiptNumber}`);
      console.log(`付款日期: ${invoice.datePaid}`);
      console.log(`付款方式: ${invoice.paymentMethod}`);
      console.log(`金额: ${invoice.amount}`);
      console.log(`产品: ${invoice.description}`);
      console.log(`服务期间: ${invoice.dateRange}`);
      console.log('');
      console.log('收票人信息:');
      console.log(`  姓名: ${invoice.billTo.name}`);
      console.log(`  地址1: ${invoice.billTo.address1}`);
      console.log(`  地址2: ${invoice.billTo.address2}`);
      console.log(`  城市: ${invoice.billTo.city}`);
      console.log(`  州/省: ${invoice.billTo.state}`);
      console.log(`  国家: ${invoice.billTo.country}`);
      console.log(`  邮箱: ${invoice.billTo.email}`);
      console.log('');
      console.log('公司信息:');
      console.log(`  公司: ${invoice.companyInfo.name}`);
      console.log(`  地址1: ${invoice.companyInfo.address1}`);
      console.log(`  地址2: ${invoice.companyInfo.address2}`);
      console.log(`  邮箱: ${invoice.companyInfo.email}`);
      console.log('\n' + '='.repeat(50) + '\n');
      
    } catch (error) {
      console.error(`${testCase.type} 生成失败:`, error);
    }
  }
};

// 性能测试
const testPerformance = async () => {
  console.log('=== 本地生成器性能测试 ===\n');
  
  const iterations = 50;
  const locale = 'en_US';
  
  console.log(`生成 ${iterations} 个Invoice...`);
  const startTime = Date.now();
  
  const promises = [];
  for (let i = 0; i < iterations; i++) {
    promises.push(generateRandomInvoiceLocal(`test${i}@example.com`, InvoiceType.WINDSURF, locale));
  }
  
  try {
    await Promise.all(promises);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`完成时间: ${duration}ms`);
    console.log(`平均每个: ${(duration / iterations).toFixed(2)}ms`);
    console.log(`每秒生成: ${(iterations / (duration / 1000)).toFixed(2)} 个Invoice\n`);
  } catch (error) {
    console.error('性能测试失败:', error);
  }
};

// 运行所有测试
const runAllTests = async () => {
  try {
    await testInvoiceGeneration();
    await testPerformance();
    
    console.log('✅ 所有测试完成！');
    console.log('\n🎉 本地Invoice生成器工作正常！');
    console.log('📝 主要特性:');
    console.log('   • 支持多种Invoice类型');
    console.log('   • 本地化收票人信息');
    console.log('   • 高性能批量生成');
    console.log('   • 完整的Invoice数据结构');
    console.log('   • 零网络依赖');
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
};

// 执行测试
runAllTests();
